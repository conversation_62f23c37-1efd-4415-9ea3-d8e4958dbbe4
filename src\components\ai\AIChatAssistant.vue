<template>
  <div class="ai-chat-container" data-testid="ai-chat-container">
    <!-- Chat Toggle Button -->
    <div
      v-if="!aiChatStore.isOpen"
      @click="toggleChat"
      class="chat-toggle-btn zb-style-chat-btn"
      :class="{
        'pulse': aiChatStore.hasUnreadMessage,
        'landing-position': isLandingPage,
        'community-position': isCommunityPage
      }"
      data-testid="ai-chat-toggle"
      role="button"
      tabindex="0"
      @keydown.enter="toggleChat"
      @keydown.space="toggleChat"
    >
      <div class="chat-btn-inner">
        <q-icon name="chat" size="24px" color="white" />
      </div>
      <q-tooltip class="bg-primary">AI Assistant</q-tooltip>
    </div>

    <!-- Chat Window -->
    <q-card v-if="aiChatStore.isOpen" class="chat-window" :class="{
      'landing-position': isLandingPage,
      'community-position': isCommunityPage
    }">
      <!-- Header -->
      <q-card-section class="chat-header bg-primary text-white">
        <div class="row items-center justify-between">
          <div class="row items-center">
            <q-icon name="psychology" size="24px" class="q-mr-sm" />
            <div>
              <div class="text-weight-bold">ZbInnovation AI</div>
              <div class="text-caption">Your Innovation Assistant</div>
            </div>
          </div>
          <q-btn
            @click="closeChat"
            flat
            round
            icon="close"
            size="sm"
            class="text-white"
          />
        </div>
      </q-card-section>

      <!-- Messages Area -->
      <q-card-section class="chat-messages" ref="messagesContainer">
        <div
          v-for="(message, index) in aiChatStore.messages"
          :key="message.id"
          class="message"
          :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
          :data-testid="message.role === 'assistant' ? 'ai-message' : 'user-message'"
        >
          <div class="message-content">
            <div v-if="message.role === 'assistant'" class="ai-avatar">
              <q-icon name="smart_toy" size="20px" />
            </div>
            <div class="message-text" v-html="formatMessage(message.content)"></div>
            <div v-if="message.role === 'user'" class="user-avatar">
              <q-icon name="person" size="20px" />
            </div>
          </div>

          <!-- Action Buttons for AI messages - positioned after message content -->
          <div v-if="message.role === 'assistant' && message.action_buttons && message.action_buttons.length > 0"
               class="action-buttons q-mt-sm">
            <div class="row q-gutter-xs">
              <q-btn
                v-for="action in message.action_buttons"
                :key="action.id"
                :label="action.label"
                :icon="action.icon"
                :color="action.color || 'primary'"
                size="sm"
                outline
                @click="handleActionClick(action)"
                :loading="actionLoading[action.id]"
                class="action-btn"
              >
                <q-tooltip v-if="action.tooltip">{{ action.tooltip }}</q-tooltip>
              </q-btn>
            </div>
          </div>
        </div>

        <!-- Loading indicator -->
        <div v-if="aiChatStore.isLoading" class="message ai-message">
          <div class="message-content">
            <div class="ai-avatar">
              <q-icon name="smart_toy" size="20px" />
            </div>
            <div class="message-text">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>

      <!-- Quick Reply Suggestions -->
      <q-card-section v-if="quickReplySuggestions.length > 0" class="quick-replies q-pt-none">
        <div class="text-caption text-grey-6 q-mb-sm">Quick replies:</div>
        <div class="row q-gutter-xs">
          <q-chip
            v-for="suggestion in quickReplySuggestions"
            :key="suggestion"
            clickable
            outline
            color="primary"
            size="sm"
            @click="selectQuickReply(suggestion)"
            class="quick-reply-chip"
          >
            {{ suggestion }}
          </q-chip>
        </div>
      </q-card-section>

      <!-- Input Area -->
      <q-card-section class="chat-input">
        <form @submit.prevent="sendMessage" class="row items-center q-gutter-sm">
          <q-input
            v-model="currentMessage"
            placeholder="Type your message..."
            outlined
            dense
            class="col"
            :disable="aiChatStore.isLoading"
            data-testid="ai-chat-input"
            @keydown.enter.prevent="sendMessage"
          />
          <q-btn
            @click="sendMessage"
            icon="send"
            color="primary"
            round
            :disable="!canSendMessage"
            data-testid="ai-chat-send"
          />
        </form>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useAIChatStore } from '@/stores/aiChat'
import { useAuthStore } from '@/stores/auth'
import { useGlobalServicesStore } from '@/stores/globalServices'

// Stores
const aiChatStore = useAIChatStore()
const authStore = useAuthStore()
const globalServices = useGlobalServicesStore()
const route = useRoute()

// Reactive data
const currentMessage = ref('')
const messagesContainer = ref<HTMLElement | null>(null)
const actionLoading = ref<Record<string, boolean>>({})
const quickReplySuggestions = ref<string[]>([])

// Computed properties
const canSendMessage = computed(() => 
  currentMessage.value.trim().length > 0 && !aiChatStore.isLoading
)

const isLandingPage = computed(() => route.path === '/')
const isCommunityPage = computed(() => 
  route.path.includes('/virtual-community') || route.path.includes('/community')
)

// Methods
const toggleChat = () => {
  aiChatStore.toggleChat()
  if (aiChatStore.isOpen) {
    scrollToBottomWithDelay()
  }
}

const closeChat = () => {
  aiChatStore.closeChat()
}

const sendMessage = async () => {
  const messageText = currentMessage.value.trim()
  if (!messageText || aiChatStore.isLoading) return

  currentMessage.value = ''

  // Clear quick replies after sending a message
  quickReplySuggestions.value = []

  await scrollToBottomWithDelay()

  try {
    console.log('🚀 Sending message:', messageText)
    await aiChatStore.sendAIMessage(messageText)
    console.log('✅ Message sent successfully')

    // Generate new quick replies after AI response
    await generateQuickReplies()

    await scrollToBottomWithDelay()
  } catch (error: any) {
    console.error('💥 Error sending message:', error)
  }
}

const selectQuickReply = (suggestion: string) => {
  currentMessage.value = suggestion
  sendMessage()
}

const generateQuickReplies = async () => {
  try {
    // Get contextual suggestions based on user state and last AI message
    const userContext = {
      is_authenticated: authStore.isAuthenticated,
      user_id: authStore.user?.id,
      profile_type: authStore.profile?.profile_type,
      profile_completion: authStore.profile?.profile_completion || 0,
      current_page: route.name as string || 'unknown'
    }

    const lastMessage = aiChatStore.lastMessage
    const suggestions = await globalServices.aiChatService.generateContextualSuggestions(
      userContext,
      lastMessage?.content || ''
    )

    quickReplySuggestions.value = suggestions.slice(0, 4) // Limit to 4 suggestions
    console.log('🎯 Generated quick replies:', quickReplySuggestions.value)
  } catch (error) {
    console.error('Error generating quick replies:', error)
    quickReplySuggestions.value = []
  }
}

const formatMessage = (content: string): string => {
  // Convert markdown-style formatting to HTML
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/\n/g, '<br>')
}

const handleActionClick = async (action: any) => {
  try {
    console.log('🎯 Action button clicked:', action)

    // Set loading state
    actionLoading.value[action.id] = true

    // Execute action through global services
    const result = await globalServices.aiActionService.executeAction(action)

    if (result.success) {
      console.log('✅ Action executed successfully:', result.message)

      // Add a system message to show action was executed
      if (result.message) {
        aiChatStore.addMessage({
          role: 'assistant',
          content: `✅ ${result.message}`
        })

        // Scroll to show the new message
        await scrollToBottomWithDelay()
      }
    } else {
      console.error('❌ Action execution failed:', result.error)

      // Add error message to chat
      aiChatStore.addMessage({
        role: 'assistant',
        content: `❌ ${result.error || 'Action failed to execute'}`
      })

      await scrollToBottomWithDelay()
    }

  } catch (error: any) {
    console.error('💥 Error executing action:', error)

    // Add error message to chat
    aiChatStore.addMessage({
      role: 'assistant',
      content: `❌ Error: ${error.message || 'Failed to execute action'}`
    })

    await scrollToBottomWithDelay()
  } finally {
    // Clear loading state
    actionLoading.value[action.id] = false
  }
}

const scrollToBottomWithDelay = async (delay: number = 100) => {
  await nextTick()
  setTimeout(() => {
    scrollToBottom()
  }, delay)
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    console.log('📜 Scrolled to bottom')
  }
}

// Event listeners
const handleScrollEvent = () => {
  scrollToBottomWithDelay()
}

const handleAITriggerEvent = (event: CustomEvent) => {
  const { message, context } = event.detail

  // Open chat if not already open
  if (!aiChatStore.isOpen) {
    aiChatStore.openChat()
  }

  // Set the prefilled message
  userMessage.value = message

  // Focus on input after a short delay
  setTimeout(() => {
    const input = document.querySelector('.chat-input input') as HTMLInputElement
    if (input) {
      input.focus()
    }
  }, 100)

  console.log('🎯 AI trigger activated:', { message: message.substring(0, 50) + '...', context })
}

onMounted(() => {
  // Set current route for AI context
  aiChatStore.setCurrentRoute(route.name as string || 'unknown')

  // Generate initial quick replies
  generateQuickReplies()

  // Listen for scroll events
  window.addEventListener('ai-trigger-scroll', handleScrollEvent)

  // Listen for AI trigger events
  window.addEventListener('ai-trigger-chat', handleAITriggerEvent as EventListener)
})

onUnmounted(() => {
  window.removeEventListener('ai-trigger-scroll', handleScrollEvent)
  window.removeEventListener('ai-trigger-chat', handleAITriggerEvent as EventListener)
})
</script>

<style scoped>
.ai-chat-container {
  position: fixed;
  z-index: 9999;
}

/* ZB-Style Chat Button */
.zb-style-chat-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #0D8A3E 0%, #0B7235 100%);
  box-shadow: 0 4px 20px rgba(13, 138, 62, 0.4);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  outline: none;
}

.zb-style-chat-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(13, 138, 62, 0.5);
  background: linear-gradient(135deg, #0F9B45 0%, #0D8A3E 100%);
}

.zb-style-chat-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 15px rgba(13, 138, 62, 0.4);
}

.zb-style-chat-btn.landing-position {
  bottom: 80px; /* Above news ticker */
}

.zb-style-chat-btn.community-position {
  bottom: 80px; /* Above create post button */
}

.zb-style-chat-btn.pulse {
  animation: zb-pulse 2s infinite;
}

.chat-btn-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

@keyframes zb-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(13, 138, 62, 0.4);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(13, 138, 62, 0.6);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4px 20px rgba(13, 138, 62, 0.4);
  }
}

.chat-window {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  max-width: 90vw;
  height: 500px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  z-index: 9999;
}

.chat-window.landing-position {
  bottom: 80px;
}

.chat-window.community-position {
  bottom: 80px;
}

.chat-header {
  flex-shrink: 0;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  max-height: 350px;
  padding: 16px;
}

.message {
  margin-bottom: 16px;
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.user-message .message-content {
  flex-direction: row-reverse;
}

.ai-avatar, .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.ai-avatar {
  background-color: #e3f2fd;
  color: #1976d2;
}

.user-avatar {
  background-color: #f3e5f5;
  color: #7b1fa2;
}

.message-text {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 12px;
  max-width: 280px;
  word-wrap: break-word;
}

.user-message .message-text {
  background-color: #e3f2fd;
  margin-left: auto;
}

.chat-input {
  flex-shrink: 0;
  border-top: 1px solid #e0e0e0;
}

.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) { animation-delay: -0.32s; }
.typing-indicator span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

/* Action buttons styling */
.action-buttons {
  margin-top: 8px;
}

.action-btn {
  font-size: 0.75rem;
  min-height: 28px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Quick reply suggestions styling */
.quick-replies {
  border-top: 1px solid #e0e0e0;
  background-color: #fafafa;
}

.quick-reply-chip {
  font-size: 0.75rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.quick-reply-chip:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 600px) {
  .chat-window {
    width: 95vw;
    height: 70vh;
    bottom: 10px;
    right: 2.5vw;
  }

  .zb-style-chat-btn {
    bottom: 15px;
    right: 15px;
    width: 56px;
    height: 56px;
  }

  .zb-style-chat-btn.landing-position,
  .zb-style-chat-btn.community-position {
    bottom: 75px;
  }
}
</style>
