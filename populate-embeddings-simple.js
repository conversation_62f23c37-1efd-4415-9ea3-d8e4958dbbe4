#!/usr/bin/env node

/**
 * Simple embedding population script
 */

const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

console.log('🧠 Simple Embedding Population');
console.log('==============================');

// Fast embedding generation
function generateFastEmbedding(text, dimensions = 384) {
  const embedding = new Array(dimensions).fill(0);
  
  const cleanText = text.toLowerCase().trim();
  const words = cleanText.split(/\s+/).filter(w => w.length > 0);
  const chars = cleanText.split('');
  
  for (let i = 0; i < dimensions; i++) {
    let value = 0;
    
    if (i < chars.length) {
      const char = chars[i];
      value += char.charCodeAt(0) / 127.0;
    }
    
    const wordIndex = i % words.length;
    if (words[wordIndex]) {
      const word = words[wordIndex];
      value += word.length / 15.0;
      value += word.charCodeAt(0) / 127.0;
    }
    
    value += Math.sin(i * 0.1) * 0.1;
    value += Math.cos(i * 0.05) * 0.1;
    
    const textHash = simpleHash(cleanText + i.toString());
    value += (textHash % 100) / 100.0;
    
    const semanticWords = ['innovation', 'technology', 'startup', 'investor', 'mentor', 'ai', 'business'];
    semanticWords.forEach((semWord, idx) => {
      if (cleanText.includes(semWord)) {
        value += Math.sin((i + idx) * 0.2) * 0.2;
      }
    });
    
    embedding[i] = Math.tanh(value);
  }
  
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
  if (magnitude > 0) {
    for (let i = 0; i < embedding.length; i++) {
      embedding[i] /= magnitude;
    }
  }
  
  return embedding;
}

function simpleHash(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash);
}

async function populateEmbeddings() {
  console.log('📊 Fetching user profiles...');
  
  // Get profiles
  const response = await fetch(`${SUPABASE_URL}/rest/v1/personal_details?select=user_id,first_name,last_name,profile_name,bio,profile_type,profile_completion`, {
    headers: {
      'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
      'apikey': SUPABASE_ANON_KEY,
    }
  });

  const profiles = await response.json();
  console.log(`✅ Found ${profiles.length} profiles`);
  
  let successCount = 0;
  
  for (let i = 0; i < profiles.length; i++) {
    const profile = profiles[i];
    
    // Create content
    const parts = [];
    if (profile.profile_name) parts.push(profile.profile_name);
    if (profile.first_name && profile.last_name) {
      parts.push(`${profile.first_name} ${profile.last_name}`);
    }
    if (profile.profile_type) parts.push(`Profile type: ${profile.profile_type}`);
    if (profile.bio) parts.push(profile.bio);
    
    const content = parts.join('. ').trim();
    
    if (content.length < 10) {
      console.log(`  ${i + 1}/${profiles.length}: ${profile.profile_name || profile.first_name} - Skipped (insufficient content)`);
      continue;
    }
    
    console.log(`  ${i + 1}/${profiles.length}: ${profile.profile_name || profile.first_name} (${profile.profile_type})`);
    
    try {
      // Generate embedding
      const embedding = generateFastEmbedding(content);
      
      // Simple SQL insert without ON CONFLICT
      const sql = `INSERT INTO embeddings (content, metadata, embedding, source_table, source_id) 
                   VALUES (
                     '${content.replace(/'/g, "''")}',
                     '${JSON.stringify({
                       type: 'profile',
                       profile_type: profile.profile_type,
                       user_id: profile.user_id,
                       profile_name: profile.profile_name,
                       profile_completion: profile.profile_completion
                     })}',
                     '[${embedding.join(',')}]'::vector,
                     'personal_details',
                     '${profile.user_id}'
                   );`;
      
      const insertResponse = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_sql_direct`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'apikey': SUPABASE_ANON_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sql_query: sql
        })
      });

      if (insertResponse.ok) {
        console.log(`    ✅ Embedding created successfully`);
        successCount++;
      } else {
        const errorText = await insertResponse.text();
        console.log(`    ❌ Insert failed: ${errorText.substring(0, 100)}`);
      }
      
    } catch (error) {
      console.log(`    ❌ Error: ${error.message}`);
    }
    
    // Small delay
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n📊 Results: ${successCount}/${profiles.length} embeddings created`);
  return successCount;
}

async function testRAG() {
  console.log('\n🔍 Testing RAG with populated embeddings...');
  
  const testQueries = [
    'AI technology innovators',
    'experienced mentors',
    'fintech investors'
  ];
  
  for (const query of testQueries) {
    console.log(`\n🔎 Testing: "${query}"`);
    
    try {
      const queryEmbedding = generateFastEmbedding(query);
      
      const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/get_rag_context`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'apikey': SUPABASE_ANON_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query_embedding: `[${queryEmbedding.join(',')}]`,
          user_id_param: '12345678-1234-1234-1234-123456789012',
          max_context_items: 3,
          similarity_threshold: 0.3
        })
      });

      if (response.ok) {
        const ragData = await response.json();
        console.log(`  ✅ Found ${ragData.length} relevant profiles`);
        
        ragData.forEach((item, index) => {
          console.log(`    ${index + 1}. ${item.metadata?.profile_name || 'Unknown'} (${item.metadata?.profile_type}) - Score: ${item.relevance_score?.toFixed(3)}`);
        });
      } else {
        console.log(`  ❌ RAG test failed: ${response.status}`);
      }
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
    }
  }
}

async function runSimplePopulation() {
  const successCount = await populateEmbeddings();
  
  if (successCount > 0) {
    await testRAG();
    console.log('\n🎉 Simple embedding population complete!');
  } else {
    console.log('\n❌ No embeddings were created');
  }
}

runSimplePopulation().catch(console.error);
