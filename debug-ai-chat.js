#!/usr/bin/env node

/**
 * Debug AI Chat Function
 */

const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

console.log('🔧 Debugging AI Chat Function');
console.log('==============================');

async function testAIChatDirect() {
  console.log('📞 Testing AI chat function directly...');
  
  try {
    const startTime = Date.now();
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'hi',
        rag_enabled: false, // Disable RAG to avoid slow embedding
        user_context: {
          is_authenticated: true,
          user_id: 'e42a885b-b9e7-45d1-8ec5-9644736b3b9d',
          profile_type: 'mentor',
          profile_completion: 83,
          current_page: 'dashboard'
        }
      })
    });

    const responseTime = Date.now() - startTime;
    console.log(`⏱️  Response time: ${responseTime}ms`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ AI Chat Response:');
      console.log(`  Success: ${data.success}`);
      console.log(`  Message: "${data.message?.substring(0, 200)}..."`);
      console.log(`  Route: ${data.query_route}`);
      console.log(`  Processing time: ${data.processing_time_ms}ms`);
      
      if (data.error) {
        console.log(`  Error: ${data.error}`);
      }
      
      return data;
    } else {
      const errorText = await response.text();
      console.log(`❌ AI Chat failed: ${response.status}`);
      console.log(`Error: ${errorText}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return null;
  }
}

async function testSimpleQuery() {
  console.log('\n📊 Testing simple query without RAG...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'How many users are on the platform?',
        rag_enabled: false,
        user_context: {
          is_authenticated: true,
          user_id: 'e42a885b-b9e7-45d1-8ec5-9644736b3b9d',
          profile_type: 'mentor'
        }
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Simple query response:');
      console.log(`  Route: ${data.query_route}`);
      console.log(`  Message: "${data.message?.substring(0, 150)}..."`);
      return data;
    } else {
      console.log(`❌ Simple query failed: ${response.status}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return null;
  }
}

async function testQueryRouter() {
  console.log('\n🎯 Testing query router function...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/functions/v1/query-router`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'hi',
        user_context: {
          profile_type: 'mentor'
        }
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ Query router response:');
      console.log(`  Route: ${data.route}`);
      console.log(`  Confidence: ${data.confidence}`);
      console.log(`  Reasoning: ${data.reasoning}`);
      return data;
    } else {
      console.log(`❌ Query router failed: ${response.status}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return null;
  }
}

async function checkEdgeFunctionLogs() {
  console.log('\n📋 Checking edge function status...');
  
  try {
    // Test if functions are accessible
    const functions = ['ai-chat', 'query-router', 'text2sql'];
    
    for (const func of functions) {
      try {
        const response = await fetch(`${SUPABASE_URL}/functions/v1/${func}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ test: true })
        });
        
        console.log(`  ${func}: ${response.status} ${response.statusText}`);
      } catch (error) {
        console.log(`  ${func}: Error - ${error.message}`);
      }
    }
  } catch (error) {
    console.log(`❌ Error checking functions: ${error.message}`);
  }
}

async function runDiagnostics() {
  console.log('🚀 Starting AI chat diagnostics...\n');
  
  await checkEdgeFunctionLogs();
  await testQueryRouter();
  await testSimpleQuery();
  await testAIChatDirect();
  
  console.log('\n🔍 Diagnosis Summary:');
  console.log('1. Check if AI chat function is responding');
  console.log('2. Test without RAG to avoid slow embedding');
  console.log('3. Verify query routing is working');
  console.log('4. Check for any error messages');
  
  console.log('\n💡 If AI chat is not responding:');
  console.log('- The embedding function might be timing out');
  console.log('- Try disabling RAG temporarily');
  console.log('- Check Supabase function logs for errors');
}

runDiagnostics().catch(console.error);
