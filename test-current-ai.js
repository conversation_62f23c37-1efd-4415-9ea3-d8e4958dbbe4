#!/usr/bin/env node

/**
 * Test Current AI Integration
 * Tests the actual deployed functions to identify issues
 */

const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

console.log('🧪 Testing Current AI Integration');
console.log('=================================');
console.log(`Supabase URL: ${SUPABASE_URL}`);
console.log('');

/**
 * Test text2sql function
 */
async function testText2SQL() {
  console.log('📊 Testing text2sql function...');
  
  const testQueries = [
    'How many innovators are on the platform?',
    'Show me platform statistics',
    'List all investors'
  ];

  for (const query of testQueries) {
    try {
      console.log(`  🔍 Testing: "${query}"`);
      
      const response = await fetch(`${SUPABASE_URL}/functions/v1/text2sql`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          max_results: 10
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`    ✅ Success: ${data.results?.length || 0} results`);
        console.log(`    📝 SQL: ${data.sql_query?.substring(0, 80)}...`);
        console.log(`    ⏱️  Time: ${data.processing_time_ms}ms`);
      } else {
        console.log(`    ❌ Failed: ${data.error || 'Unknown error'}`);
        console.log(`    📄 Response:`, JSON.stringify(data, null, 2));
      }
    } catch (error) {
      console.log(`    ❌ Error: ${error.message}`);
    }
    console.log('');
  }
}

/**
 * Test query-router function
 */
async function testQueryRouter() {
  console.log('🎯 Testing query-router function...');
  
  const testQueries = [
    'How many users are there?',
    'Tell me about innovators on the platform',
    'Find me some mentors with AI expertise'
  ];

  for (const query of testQueries) {
    try {
      console.log(`  🔍 Testing: "${query}"`);
      
      const response = await fetch(`${SUPABASE_URL}/functions/v1/query-router`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          user_context: {
            user_id: 'test-user',
            profile_type: 'innovator'
          }
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`    ✅ Route: ${data.route} (confidence: ${data.confidence})`);
        console.log(`    💭 Reasoning: ${data.reasoning}`);
        console.log(`    ⏱️  Time: ${data.processing_time_ms}ms`);
      } else {
        console.log(`    ❌ Failed: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`    ❌ Error: ${error.message}`);
    }
    console.log('');
  }
}

/**
 * Test embed function
 */
async function testEmbed() {
  console.log('🧠 Testing embed function...');
  
  const testTexts = [
    'I am an innovator working on AI technology',
    'Looking for investors in fintech space'
  ];

  for (const text of testTexts) {
    try {
      console.log(`  🔍 Testing: "${text}"`);
      
      const response = await fetch(`${SUPABASE_URL}/functions/v1/embed`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          input: text,
          model: 'bge-m3'
        })
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        console.log(`    ✅ Success: ${data.dimensions} dimensions`);
        console.log(`    📊 Model: ${data.model}`);
        console.log(`    ⏱️  Time: ${data.processing_time_ms}ms`);
      } else {
        console.log(`    ❌ Failed: ${data.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`    ❌ Error: ${error.message}`);
    }
    console.log('');
  }
}

/**
 * Test database functions
 */
async function testDatabaseFunctions() {
  console.log('🗄️  Testing database functions...');
  
  try {
    // Test get_rag_context
    console.log('  📊 Testing get_rag_context...');
    const ragResponse = await fetch(`${SUPABASE_URL}/rest/v1/rpc/get_rag_context`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query_embedding: '[' + Array(384).fill(0.1).join(',') + ']',
        max_context_items: 5
      })
    });

    if (ragResponse.ok) {
      const ragData = await ragResponse.json();
      console.log(`    ✅ get_rag_context works: ${ragData.length} results`);
    } else {
      console.log(`    ❌ get_rag_context failed: ${ragResponse.status}`);
    }

    // Test analyze_rag_system
    console.log('  📈 Testing analyze_rag_system...');
    const analysisResponse = await fetch(`${SUPABASE_URL}/rest/v1/rpc/analyze_rag_system`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });

    if (analysisResponse.ok) {
      const analysisData = await analysisResponse.json();
      console.log(`    ✅ analyze_rag_system works`);
      console.log(`    📊 Results:`, analysisData);
    } else {
      console.log(`    ❌ analyze_rag_system failed: ${analysisResponse.status}`);
    }

  } catch (error) {
    console.log(`    ❌ Database function test error: ${error.message}`);
  }
}

/**
 * Test missing exec_sql_direct function
 */
async function testExecSQLDirect() {
  console.log('🔧 Testing exec_sql_direct function...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/exec_sql_direct`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sql_query: 'SELECT COUNT(*) as total FROM personal_details'
      })
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`    ✅ exec_sql_direct works: ${JSON.stringify(data)}`);
    } else {
      console.log(`    ❌ exec_sql_direct missing or failed: ${response.status}`);
      const errorText = await response.text();
      console.log(`    📄 Error details: ${errorText}`);
    }
  } catch (error) {
    console.log(`    ❌ exec_sql_direct test error: ${error.message}`);
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting AI integration tests...\n');
  
  await testText2SQL();
  await testQueryRouter();
  await testEmbed();
  await testDatabaseFunctions();
  await testExecSQLDirect();
  
  console.log('✅ All tests completed!');
}

// Run tests
runAllTests().catch(console.error);
