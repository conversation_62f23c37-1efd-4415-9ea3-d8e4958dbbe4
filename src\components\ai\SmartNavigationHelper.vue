<template>
  <div v-if="showHelper" class="smart-navigation-helper">
    <!-- Navigation Suggestions -->
    <q-card v-if="suggestions.length > 0" class="navigation-suggestions-card q-mb-md" flat bordered>
      <q-card-section class="q-pb-sm">
        <div class="row items-center">
          <div class="col">
            <div class="text-h6 text-primary">
              <q-icon name="explore" class="q-mr-sm" />
              Smart Navigation
            </div>
            <div class="text-caption text-grey-6">
              Suggested next steps for you
            </div>
          </div>
          <div class="col-auto">
            <q-btn
              flat
              dense
              round
              icon="close"
              @click="hideHelper"
              class="text-grey-6"
            />
          </div>
        </div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <div class="row q-gutter-sm">
          <div
            v-for="suggestion in suggestions.slice(0, maxSuggestions)"
            :key="suggestion.id"
            class="col-12 col-sm-6 col-md-4"
          >
            <q-card
              flat
              bordered
              class="suggestion-card cursor-pointer"
              @click="handleSuggestionClick(suggestion)"
            >
              <q-card-section class="q-pa-sm">
                <div class="row items-center q-gutter-sm">
                  <div class="col-auto">
                    <q-icon
                      :name="suggestion.icon"
                      :color="getCategoryColor(suggestion.category)"
                      size="md"
                    />
                  </div>
                  <div class="col">
                    <div class="text-subtitle2 text-weight-medium">
                      {{ suggestion.title }}
                    </div>
                    <div class="text-caption text-grey-6">
                      {{ suggestion.description }}
                    </div>
                    <div class="text-caption text-primary q-mt-xs">
                      {{ suggestion.reason }}
                    </div>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Contextual Help -->
    <q-card v-if="contextualHelp.length > 0" class="contextual-help-card" flat bordered>
      <q-card-section class="q-pb-sm">
        <div class="text-h6 text-secondary">
          <q-icon name="help_outline" class="q-mr-sm" />
          Quick Help
        </div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <div v-for="help in contextualHelp" :key="help.id" class="help-item q-mb-sm">
          <q-banner
            :class="`bg-${getHelpTypeColor(help.type)}-1`"
            rounded
          >
            <template v-slot:avatar>
              <q-icon
                :name="help.icon"
                :color="getHelpTypeColor(help.type)"
              />
            </template>

            <div class="text-subtitle2 text-weight-medium">{{ help.title }}</div>
            <div class="text-body2 q-mt-xs">{{ help.content }}</div>

            <template v-if="help.actions && help.actions.length > 0" v-slot:action>
              <q-btn
                v-for="action in help.actions"
                :key="action.label"
                :color="getHelpTypeColor(help.type)"
                :label="action.label"
                flat
                dense
                @click="handleHelpAction(action)"
                class="q-ml-sm"
              />
            </template>
          </q-banner>
        </div>
      </q-card-section>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { aiSmartNavigationService, type NavigationSuggestion, type ContextualHelp } from '@/services/aiSmartNavigationService'

// Props
interface Props {
  page: string
  maxSuggestions?: number
  showHelp?: boolean
  autoHide?: boolean
  hideAfterSeconds?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxSuggestions: 3,
  showHelp: true,
  autoHide: false,
  hideAfterSeconds: 10
})

// Emits
const emit = defineEmits<{
  suggestionClicked: [suggestion: NavigationSuggestion]
  helpActionClicked: [action: any]
  hidden: []
}>()

// Composables
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const suggestions = ref<NavigationSuggestion[]>([])
const contextualHelp = ref<ContextualHelp[]>([])
const showHelper = ref(true)
const hideTimeout = ref<number | null>(null)

// Computed properties
const navigationContext = computed(() => ({
  current_page: props.page,
  user_profile: authStore.profile,
  profile_completion: authStore.profile?.profile_completion || 0,
  recent_pages: [], // Could be enhanced with actual tracking
  time_on_page: 0, // Could be enhanced with actual tracking
  user_intent: 'browse' // Could be enhanced with intent detection
}))

// Methods
const loadNavigationData = () => {
  try {
    // Get navigation suggestions
    suggestions.value = aiSmartNavigationService.getNavigationSuggestions(navigationContext.value)
    
    // Get contextual help if enabled
    if (props.showHelp) {
      contextualHelp.value = aiSmartNavigationService.getContextualHelp(navigationContext.value)
    }

    console.log('🧭 Smart navigation loaded:', {
      suggestions: suggestions.value.length,
      help: contextualHelp.value.length
    })

    // Set auto-hide timer if enabled
    if (props.autoHide && props.hideAfterSeconds > 0) {
      hideTimeout.value = window.setTimeout(() => {
        hideHelper()
      }, props.hideAfterSeconds * 1000)
    }

  } catch (error) {
    console.error('Error loading navigation data:', error)
  }
}

const handleSuggestionClick = (suggestion: NavigationSuggestion) => {
  try {
    emit('suggestionClicked', suggestion)
    
    // Track navigation
    aiSmartNavigationService.trackNavigation(
      props.page,
      suggestion.route,
      'suggestion'
    )

    // Navigate to suggested route
    router.push(suggestion.route)

    console.log('🎯 Navigation suggestion clicked:', suggestion.title)
  } catch (error) {
    console.error('Error handling suggestion click:', error)
  }
}

const handleHelpAction = (action: any) => {
  try {
    emit('helpActionClicked', action)

    switch (action.action) {
      case 'navigate':
        router.push(action.data.route)
        break
      case 'trigger':
        // Emit custom event for AI triggers
        const event = new CustomEvent('ai-navigation-trigger', {
          detail: action.data
        })
        window.dispatchEvent(event)
        break
      case 'external':
        window.open(action.data.url, '_blank')
        break
    }

    console.log('🔧 Help action clicked:', action.label)
  } catch (error) {
    console.error('Error handling help action:', error)
  }
}

const hideHelper = () => {
  showHelper.value = false
  emit('hidden')
  
  if (hideTimeout.value) {
    clearTimeout(hideTimeout.value)
    hideTimeout.value = null
  }
}

const getCategoryColor = (category: string): string => {
  const colors: Record<string, string> = {
    'profile': 'primary',
    'networking': 'blue',
    'content': 'green',
    'discovery': 'purple',
    'help': 'orange'
  }
  return colors[category] || 'grey'
}

const getHelpTypeColor = (type: string): string => {
  const colors: Record<string, string> = {
    'tip': 'blue',
    'guide': 'green',
    'warning': 'orange',
    'info': 'grey'
  }
  return colors[type] || 'grey'
}

// Lifecycle
onMounted(() => {
  loadNavigationData()
})

// Watch for route changes
watch(() => route.path, () => {
  loadNavigationData()
})

// Watch for auth changes
watch(() => authStore.user, () => {
  loadNavigationData()
}, { deep: true })
</script>

<style scoped>
.smart-navigation-helper {
  position: relative;
}

.navigation-suggestions-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.contextual-help-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
}

.suggestion-card {
  transition: all 0.2s ease;
  border-radius: 6px;
}

.suggestion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: var(--q-primary);
}

.help-item {
  border-radius: 6px;
}

/* Animation for helper appearance */
.smart-navigation-helper {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .navigation-suggestions-card .row > div {
    min-width: 100%;
  }
}

/* Category-specific styling */
.suggestion-card[data-category="profile"] {
  border-left: 3px solid var(--q-primary);
}

.suggestion-card[data-category="networking"] {
  border-left: 3px solid var(--q-blue);
}

.suggestion-card[data-category="content"] {
  border-left: 3px solid var(--q-green);
}

.suggestion-card[data-category="discovery"] {
  border-left: 3px solid var(--q-purple);
}

.suggestion-card[data-category="help"] {
  border-left: 3px solid var(--q-orange);
}
</style>
