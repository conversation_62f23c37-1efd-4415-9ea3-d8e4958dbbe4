<template>
  <q-card v-if="shouldShow" class="profile-completion-ai-guide q-mb-md">
    <q-card-section class="q-pb-sm">
      <div class="row items-center q-gutter-sm">
        <q-icon name="psychology" color="primary" size="sm" />
        <div class="text-subtitle2 text-weight-medium">AI Profile Assistant</div>
        <q-space />
        <q-btn 
          flat 
          round 
          dense 
          icon="close" 
          size="sm" 
          @click="dismiss"
          class="text-grey-6"
        />
      </div>
    </q-card-section>

    <q-card-section class="q-pt-none">
      <!-- Milestone Celebration -->
      <div v-if="milestoneAchieved" class="milestone-celebration q-mb-md">
        <div class="text-center q-pa-md bg-positive text-white rounded-borders">
          <q-icon name="celebration" size="md" class="q-mb-sm" />
          <div class="text-h6">{{ milestoneAchieved }}</div>
          <div class="text-body2 q-mt-xs">{{ celebrationMessage }}</div>
        </div>
      </div>

      <!-- Profile Analysis Summary -->
      <div class="profile-analysis q-mb-md">
        <div class="row items-center q-gutter-sm q-mb-sm">
          <q-circular-progress
            :value="completionPercentage"
            size="40px"
            :thickness="0.15"
            color="primary"
            track-color="grey-3"
            class="text-primary"
          >
            <div class="text-caption text-weight-bold">{{ completionPercentage }}%</div>
          </q-circular-progress>
          <div>
            <div class="text-body2 text-weight-medium">Profile Completion</div>
            <div class="text-caption text-grey-6">{{ completionStageText }}</div>
          </div>
        </div>

        <!-- Profile Strengths -->
        <div v-if="profileStrengths.length > 0" class="q-mb-sm">
          <div class="text-caption text-weight-medium text-positive q-mb-xs">
            <q-icon name="check_circle" size="xs" class="q-mr-xs" />
            Strengths
          </div>
          <div class="row q-gutter-xs">
            <q-chip
              v-for="strength in profileStrengths.slice(0, 3)"
              :key="strength"
              size="sm"
              color="positive"
              text-color="white"
              :label="strength"
            />
          </div>
        </div>

        <!-- Missing Critical Fields -->
        <div v-if="missingCriticalFields.length > 0" class="q-mb-sm">
          <div class="text-caption text-weight-medium text-warning q-mb-xs">
            <q-icon name="warning" size="xs" class="q-mr-xs" />
            Missing Critical Fields
          </div>
          <div class="text-body2 text-grey-7">
            {{ missingCriticalFields.slice(0, 3).join(', ') }}
            <span v-if="missingCriticalFields.length > 3">
              and {{ missingCriticalFields.length - 3 }} more
            </span>
          </div>
        </div>
      </div>

      <!-- AI Recommendations -->
      <div v-if="nextSteps.length > 0" class="next-steps q-mb-md">
        <div class="text-body2 text-weight-medium q-mb-sm">
          <q-icon name="lightbulb" color="accent" size="sm" class="q-mr-xs" />
          AI Recommendations
        </div>
        <div class="q-gutter-xs">
          <div
            v-for="(step, index) in nextSteps.slice(0, 3)"
            :key="index"
            class="text-body2 text-grey-7 q-mb-xs"
          >
            <q-icon name="arrow_right" size="xs" class="q-mr-xs text-primary" />
            {{ step }}
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-buttons">
        <div class="row q-gutter-sm">
          <q-btn
            color="primary"
            :label="primaryActionLabel"
            :icon="primaryActionIcon"
            size="sm"
            @click="triggerPrimaryAction"
            :loading="isLoading"
          />
          <q-btn
            outline
            color="primary"
            label="Get AI Help"
            icon="psychology"
            size="sm"
            @click="triggerAIChat"
            :loading="isLoading"
          />
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useProfileStore } from '../../stores/profileStore'
import { useGlobalServicesStore } from '../../stores/globalServices'
import { aiProfileAnalyzer, type ProfileAnalysis } from '../../services/aiProfileAnalyzer'

interface Props {
  profileAnalysis?: ProfileAnalysis
  showMilestones?: boolean
  autoHide?: boolean
  context?: string
}

const props = withDefaults(defineProps<Props>(), {
  showMilestones: true,
  autoHide: false,
  context: 'profile-completion'
})

const emit = defineEmits<{
  dismissed: []
  actionTriggered: [action: string]
}>()

const profileStore = useProfileStore()
const globalServices = useGlobalServicesStore()
const isLoading = ref(false)
const isDismissed = ref(false)

// Computed properties
const shouldShow = computed(() => {
  if (isDismissed.value) return false
  if (!profileStore.currentProfile) return false
  if (props.autoHide && completionPercentage.value >= 90) return false
  return true
})

const completionPercentage = computed(() => {
  return props.profileAnalysis?.completion_percentage || 
         profileStore.currentProfile?.profile_completion || 0
})

const completionStageText = computed(() => {
  const stage = props.profileAnalysis?.completion_stage
  switch (stage) {
    case 'empty': return 'Just getting started'
    case 'basic': return 'Basic information added'
    case 'developing': return 'Profile taking shape'
    case 'advanced': return 'Almost complete'
    case 'complete': return 'Fully optimized'
    default: return 'In progress'
  }
})

const profileStrengths = computed(() => {
  return props.profileAnalysis?.strengths || []
})

const missingCriticalFields = computed(() => {
  return props.profileAnalysis?.missing_critical_fields || []
})

const nextSteps = computed(() => {
  return props.profileAnalysis?.next_steps || []
})

const milestoneAchieved = computed(() => {
  if (!props.showMilestones) return null
  return props.profileAnalysis?.milestone_achieved
})

const celebrationMessage = computed(() => {
  return props.profileAnalysis?.celebration_message || 'Great progress on your profile!'
})

const primaryActionLabel = computed(() => {
  if (completionPercentage.value < 30) return 'Complete Basic Info'
  if (completionPercentage.value < 70) return 'Improve Profile'
  if (completionPercentage.value < 90) return 'Finish Profile'
  return 'Optimize Profile'
})

const primaryActionIcon = computed(() => {
  if (completionPercentage.value < 30) return 'person_add'
  if (completionPercentage.value < 70) return 'edit'
  if (completionPercentage.value < 90) return 'check_circle'
  return 'tune'
})

// Methods
const dismiss = () => {
  isDismissed.value = true
  emit('dismissed')
}

const triggerPrimaryAction = async () => {
  try {
    isLoading.value = true
    
    // Navigate to profile completion page
    const { useRouter } = await import('vue-router')
    const router = useRouter()
    
    if (completionPercentage.value < 50) {
      router.push('/dashboard/profile/completion')
    } else {
      router.push('/dashboard/profile')
    }
    
    emit('actionTriggered', 'complete-profile')
  } catch (error) {
    console.error('Error triggering primary action:', error)
  } finally {
    isLoading.value = false
  }
}

const triggerAIChat = async () => {
  try {
    isLoading.value = true
    
    // Create context-aware message for AI
    let message = 'Help me improve my profile completion.'
    
    if (missingCriticalFields.value.length > 0) {
      message += ` I'm missing: ${missingCriticalFields.value.slice(0, 3).join(', ')}.`
    }
    
    if (completionPercentage.value < 30) {
      message += ' I need help getting started with my profile.'
    } else if (completionPercentage.value < 70) {
      message += ' I want to make my profile more complete and attractive.'
    } else {
      message += ' I want to optimize my profile for maximum visibility.'
    }
    
    // Trigger AI chat with context
    await globalServices.aiChatTriggerService.triggerChat('profile_completion_help', props.context)
    
    // Send the contextual message
    setTimeout(async () => {
      await globalServices.aiChatTriggerService.sendMessage(message)
    }, 500)
    
    emit('actionTriggered', 'ai-help')
  } catch (error) {
    console.error('Error triggering AI chat:', error)
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.profile-completion-ai-guide {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.milestone-celebration {
  animation: celebrationPulse 2s ease-in-out;
}

@keyframes celebrationPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.02); }
}

.action-buttons .q-btn {
  transition: all 0.2s ease;
}

.action-buttons .q-btn:hover {
  transform: translateY(-1px);
}
</style>
