<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Test</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-container { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .response { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 4px; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { background: #1976d2; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin: 5px; }
        button:hover { background: #1565c0; }
        button:disabled { background: #ccc; cursor: not-allowed; }
    </style>
</head>
<body>
    <h1>AI Chat Function Test</h1>
    
    <div class="test-container">
        <h2>Test AI Chat Edge Function</h2>
        <p>Testing: "which innovators are on the platform"</p>
        
        <button id="testBtn" onclick="testAIChat()">Test AI Chat</button>
        <button onclick="testText2SQL()">Test Text2SQL</button>
        
        <div id="response" class="response" style="display: none;"></div>
    </div>

    <script>
        const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

        function showResponse(data, isError = false) {
            const responseDiv = document.getElementById('response');
            responseDiv.style.display = 'block';
            responseDiv.className = `response ${isError ? 'error' : 'success'}`;
            responseDiv.textContent = JSON.stringify(data, null, 2);
        }

        async function testAIChat() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = 'Testing...';

            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: 'which innovators are on the platform',
                        conversation_history: [],
                        user_context: {
                            is_authenticated: true,
                            user_id: 'e42a885b-b9e7-45d1-8ec5-9644736b3b9d',
                            profile_type: 'mentor'
                        },
                        rag_enabled: true,
                        max_context_items: 8
                    })
                });

                const data = await response.json();
                
                if (response.ok) {
                    showResponse({
                        status: 'SUCCESS',
                        message: data.message,
                        route: data.query_route,
                        processing_time: data.processing_time_ms + 'ms'
                    });
                } else {
                    showResponse({
                        status: 'ERROR',
                        error: data,
                        status_code: response.status
                    }, true);
                }
            } catch (error) {
                showResponse({
                    status: 'NETWORK_ERROR',
                    error: error.message
                }, true);
            } finally {
                btn.disabled = false;
                btn.textContent = 'Test AI Chat';
            }
        }

        async function testText2SQL() {
            try {
                const response = await fetch(`${SUPABASE_URL}/functions/v1/text2sql`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'which innovators are on the platform',
                        max_results: 10
                    })
                });

                const data = await response.json();
                showResponse({
                    function: 'text2sql',
                    response: data
                });
            } catch (error) {
                showResponse({
                    function: 'text2sql',
                    error: error.message
                }, true);
            }
        }
    </script>
</body>
</html>
