/**
 * AI Smart Navigation Service
 * 
 * Provides intelligent navigation suggestions and contextual help
 * throughout the platform based on user behavior and profile state.
 */

import { useAuthStore } from '@/stores/auth'

export interface NavigationSuggestion {
  id: string
  title: string
  description: string
  route: string
  icon: string
  priority: number
  reason: string
  category: 'profile' | 'networking' | 'content' | 'discovery' | 'help'
  show_conditions?: {
    auth_required?: boolean
    min_profile_completion?: number
    max_profile_completion?: number
    profile_types?: string[]
    exclude_pages?: string[]
  }
}

export interface ContextualHelp {
  id: string
  title: string
  content: string
  type: 'tip' | 'guide' | 'warning' | 'info'
  icon: string
  actions?: {
    label: string
    action: 'navigate' | 'trigger' | 'external'
    data: any
  }[]
}

export interface SmartNavigationContext {
  current_page: string
  user_profile?: any
  profile_completion?: number
  recent_pages?: string[]
  time_on_page?: number
  user_intent?: string
}

export class AISmartNavigationService {

  /**
   * Get smart navigation suggestions based on current context
   */
  getNavigationSuggestions(context: SmartNavigationContext): NavigationSuggestion[] {
    const authStore = useAuthStore()
    const user = authStore.user
    const profile = authStore.profile

    // Get base suggestions
    const allSuggestions = this.getAllNavigationSuggestions(context)
    
    // Filter based on conditions
    const filteredSuggestions = allSuggestions.filter(suggestion => 
      this.shouldShowSuggestion(suggestion, user, profile, context)
    )

    // Sort by priority and relevance
    return filteredSuggestions
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 5) // Limit to top 5 suggestions
  }

  /**
   * Get contextual help for current page
   */
  getContextualHelp(context: SmartNavigationContext): ContextualHelp[] {
    const help: ContextualHelp[] = []

    // Page-specific help
    switch (context.current_page) {
      case 'profile':
        help.push(...this.getProfilePageHelp(context))
        break
      case 'dashboard':
        help.push(...this.getDashboardHelp(context))
        break
      case 'virtual-community':
        help.push(...this.getCommunityHelp(context))
        break
      case 'discover':
        help.push(...this.getDiscoveryHelp(context))
        break
    }

    // General help based on profile completion
    if (context.profile_completion && context.profile_completion < 50) {
      help.push({
        id: 'profile-completion-help',
        title: 'Complete Your Profile',
        content: 'A complete profile helps you get better recommendations and more connections.',
        type: 'tip',
        icon: 'person_add',
        actions: [
          {
            label: 'Complete Profile',
            action: 'navigate',
            data: { route: '/profile/edit' }
          }
        ]
      })
    }

    return help.slice(0, 3) // Limit to 3 help items
  }

  /**
   * Get all possible navigation suggestions
   */
  private getAllNavigationSuggestions(_context: SmartNavigationContext): NavigationSuggestion[] {
    return [
      // Profile-related suggestions
      {
        id: 'complete-profile',
        title: 'Complete Your Profile',
        description: 'Finish setting up your profile to get better recommendations',
        route: '/profile/edit',
        icon: 'person_add',
        priority: 90,
        reason: 'Profile completion improves your experience',
        category: 'profile',
        show_conditions: {
          auth_required: true,
          max_profile_completion: 70,
          exclude_pages: ['profile']
        }
      },
      {
        id: 'view-profile',
        title: 'View Your Profile',
        description: 'See how others view your profile',
        route: '/profile',
        icon: 'person',
        priority: 60,
        reason: 'Check your profile visibility',
        category: 'profile',
        show_conditions: {
          auth_required: true,
          min_profile_completion: 30,
          exclude_pages: ['profile']
        }
      },

      // Networking suggestions
      {
        id: 'discover-profiles',
        title: 'Discover Profiles',
        description: 'Find and connect with relevant people',
        route: '/virtual-community?tab=profiles',
        icon: 'people',
        priority: 80,
        reason: 'Expand your network',
        category: 'networking',
        show_conditions: {
          auth_required: true,
          min_profile_completion: 40,
          exclude_pages: ['virtual-community']
        }
      },
      {
        id: 'join-community',
        title: 'Join Community',
        description: 'Participate in community discussions',
        route: '/virtual-community?tab=feed',
        icon: 'forum',
        priority: 70,
        reason: 'Engage with the community',
        category: 'networking',
        show_conditions: {
          exclude_pages: ['virtual-community', 'community']
        }
      },

      // Content suggestions
      {
        id: 'create-post',
        title: 'Share Your Story',
        description: 'Create a post to share with the community',
        route: '/virtual-community?tab=feed&action=create',
        icon: 'edit',
        priority: 65,
        reason: 'Share your expertise',
        category: 'content',
        show_conditions: {
          auth_required: true,
          min_profile_completion: 60,
          exclude_pages: ['virtual-community']
        }
      },
      {
        id: 'browse-content',
        title: 'Browse Content',
        description: 'Explore posts and articles from the community',
        route: '/virtual-community?tab=blog',
        icon: 'article',
        priority: 50,
        reason: 'Stay informed',
        category: 'content'
      },

      // Discovery suggestions
      {
        id: 'explore-opportunities',
        title: 'Explore Opportunities',
        description: 'Find funding, partnerships, and collaborations',
        route: '/virtual-community?tab=marketplace',
        icon: 'business_center',
        priority: 75,
        reason: 'Discover new opportunities',
        category: 'discovery',
        show_conditions: {
          auth_required: true,
          profile_types: ['innovator', 'professional'],
          exclude_pages: ['virtual-community']
        }
      },
      {
        id: 'find-events',
        title: 'Upcoming Events',
        description: 'Join events and networking sessions',
        route: '/virtual-community?tab=events',
        icon: 'event',
        priority: 55,
        reason: 'Network and learn',
        category: 'discovery'
      },

      // Help suggestions
      {
        id: 'platform-guide',
        title: 'Platform Guide',
        description: 'Learn how to use the platform effectively',
        route: '/help/getting-started',
        icon: 'help',
        priority: 40,
        reason: 'Get familiar with features',
        category: 'help',
        show_conditions: {
          auth_required: true,
          max_profile_completion: 30
        }
      }
    ]
  }

  /**
   * Check if a suggestion should be shown
   */
  private shouldShowSuggestion(
    suggestion: NavigationSuggestion,
    user: any,
    profile: any,
    context: SmartNavigationContext
  ): boolean {
    const conditions = suggestion.show_conditions
    if (!conditions) return true

    // Check auth requirement
    if (conditions.auth_required && !user) {
      return false
    }

    // Check profile completion
    const profileCompletion = context.profile_completion || 0
    if (conditions.min_profile_completion && profileCompletion < conditions.min_profile_completion) {
      return false
    }
    if (conditions.max_profile_completion && profileCompletion > conditions.max_profile_completion) {
      return false
    }

    // Check profile types
    if (conditions.profile_types && profile?.profile_type && 
        !conditions.profile_types.includes(profile.profile_type)) {
      return false
    }

    // Check excluded pages
    if (conditions.exclude_pages && 
        conditions.exclude_pages.includes(context.current_page)) {
      return false
    }

    return true
  }

  /**
   * Get profile page specific help
   */
  private getProfilePageHelp(context: SmartNavigationContext): ContextualHelp[] {
    const help: ContextualHelp[] = []

    if (context.profile_completion && context.profile_completion < 100) {
      help.push({
        id: 'profile-completion-tip',
        title: 'Profile Completion Tips',
        content: 'Add more details to your profile to increase visibility and get better matches.',
        type: 'tip',
        icon: 'tips_and_updates',
        actions: [
          {
            label: 'Get AI Help',
            action: 'trigger',
            data: { trigger: 'profile-completion-help' }
          }
        ]
      })
    }

    return help
  }

  /**
   * Get dashboard specific help
   */
  private getDashboardHelp(_context: SmartNavigationContext): ContextualHelp[] {
    return [
      {
        id: 'dashboard-overview',
        title: 'Dashboard Overview',
        content: 'Your dashboard shows your profile status, recent activity, and personalized recommendations.',
        type: 'info',
        icon: 'info',
        actions: [
          {
            label: 'Take Tour',
            action: 'trigger',
            data: { trigger: 'dashboard-tour' }
          }
        ]
      }
    ]
  }

  /**
   * Get community specific help
   */
  private getCommunityHelp(_context: SmartNavigationContext): ContextualHelp[] {
    return [
      {
        id: 'community-engagement',
        title: 'Community Engagement',
        content: 'Engage with posts by commenting, sharing, and connecting with authors to build your network.',
        type: 'tip',
        icon: 'groups',
        actions: [
          {
            label: 'Learn More',
            action: 'trigger',
            data: { trigger: 'community-guide' }
          }
        ]
      }
    ]
  }

  /**
   * Get discovery specific help
   */
  private getDiscoveryHelp(_context: SmartNavigationContext): ContextualHelp[] {
    return [
      {
        id: 'discovery-filters',
        title: 'Use Smart Filters',
        content: 'Use the filter options to find exactly what you\'re looking for - people, content, or opportunities.',
        type: 'tip',
        icon: 'filter_list',
        actions: [
          {
            label: 'Show Filters',
            action: 'trigger',
            data: { trigger: 'show-filters' }
          }
        ]
      }
    ]
  }

  /**
   * Track navigation behavior for learning
   */
  trackNavigation(from: string, to: string, method: 'click' | 'suggestion' | 'search'): void {
    // This could be enhanced to track user behavior and improve suggestions
    console.log('Navigation tracked:', { from, to, method, timestamp: Date.now() })
  }

  /**
   * Get smart breadcrumbs with context
   */
  getSmartBreadcrumbs(currentRoute: string): Array<{ label: string; route?: string; icon?: string }> {
    const breadcrumbs = []

    // Always start with home
    breadcrumbs.push({ label: 'Home', route: '/dashboard', icon: 'home' })

    // Add context-specific breadcrumbs
    if (currentRoute.includes('profile')) {
      breadcrumbs.push({ label: 'Profile', route: '/profile', icon: 'person' })
    } else if (currentRoute.includes('virtual-community')) {
      breadcrumbs.push({ label: 'Community', route: '/virtual-community', icon: 'groups' })
    } else if (currentRoute.includes('discover')) {
      breadcrumbs.push({ label: 'Discover', route: '/discover', icon: 'explore' })
    }

    return breadcrumbs
  }
}

// Export singleton instance
export const aiSmartNavigationService = new AISmartNavigationService()
