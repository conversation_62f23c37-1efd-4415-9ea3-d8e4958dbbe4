#!/usr/bin/env node

/**
 * Comprehensive AI Integration Test Suite
 * Tests all AI features: text2sql, RAG, conversation memory, and context awareness
 */

const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'http://localhost:54321';
const SUPABASE_ANON_KEY = process.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key';

console.log('🧪 AI Integration Test Suite');
console.log('============================');
console.log(`Supabase URL: ${SUPABASE_URL}`);
console.log('');

// Test configurations
const TEST_CASES = [
  {
    name: 'Database Migration Test',
    description: 'Test if missing database functions are created',
    test: testDatabaseFunctions
  },
  {
    name: 'Text2SQL Function Test',
    description: 'Test text2sql Edge function with various queries',
    test: testText2SQL
  },
  {
    name: 'RAG Context Retrieval Test',
    description: 'Test RAG system with real data retrieval',
    test: testRAGContext
  },
  {
    name: 'AI Chat Integration Test',
    description: 'Test complete AI chat with context awareness',
    test: testAIChat
  },
  {
    name: 'Conversation Memory Test',
    description: 'Test conversation context storage and retrieval',
    test: testConversationMemory
  },
  {
    name: 'Authentication Awareness Test',
    description: 'Test AI responses based on authentication status',
    test: testAuthenticationAwareness
  }
];

/**
 * Test database functions
 */
async function testDatabaseFunctions() {
  console.log('  📊 Testing database functions...');
  
  try {
    // Test get_rag_context function
    const ragResponse = await fetch(`${SUPABASE_URL}/rest/v1/rpc/get_rag_context`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query_embedding: '[' + Array(384).fill(0.1).join(',') + ']',
        max_context_items: 5,
        similarity_threshold: 0.5
      })
    });

    if (ragResponse.ok) {
      console.log('    ✅ get_rag_context function exists and responds');
    } else {
      console.log('    ❌ get_rag_context function failed:', ragResponse.status);
    }

    // Test analyze_rag_system function
    const analysisResponse = await fetch(`${SUPABASE_URL}/rest/v1/rpc/analyze_rag_system`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });

    if (analysisResponse.ok) {
      const data = await analysisResponse.json();
      console.log('    ✅ analyze_rag_system function works');
      console.log(`    📈 Total embeddings: ${data[0]?.total_embeddings || 0}`);
    } else {
      console.log('    ❌ analyze_rag_system function failed:', analysisResponse.status);
    }

    return { success: true };
  } catch (error) {
    console.log('    ❌ Database function test failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test text2sql functionality
 */
async function testText2SQL() {
  console.log('  🔍 Testing text2sql function...');
  
  const testQueries = [
    'How many innovators are on the platform?',
    'Show me the latest investors',
    'Which mentors have expertise in AI?',
    'Count users by profile type'
  ];

  const results = [];

  for (const query of testQueries) {
    try {
      console.log(`    🔎 Testing: "${query}"`);
      
      const response = await fetch(`${SUPABASE_URL}/functions/v1/text2sql`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query,
          max_results: 10
        })
      });

      const data = await response.json();
      
      if (data.success) {
        console.log(`      ✅ Generated SQL: ${data.sql_query?.substring(0, 80)}...`);
        console.log(`      📊 Results: ${data.result_count} rows`);
        console.log(`      ⏱️  Time: ${data.processing_time_ms}ms`);
        results.push({ query, success: true, resultCount: data.result_count });
      } else {
        console.log(`      ❌ Failed: ${data.error}`);
        results.push({ query, success: false, error: data.error });
      }
    } catch (error) {
      console.log(`      ❌ Error: ${error.message}`);
      results.push({ query, success: false, error: error.message });
    }
  }

  const successCount = results.filter(r => r.success).length;
  console.log(`  📊 Text2SQL Results: ${successCount}/${testQueries.length} queries successful`);
  
  return { success: successCount > 0, results };
}

/**
 * Test RAG context retrieval
 */
async function testRAGContext() {
  console.log('  🧠 Testing RAG context retrieval...');
  
  try {
    // First, populate some test embeddings
    console.log('    📝 Populating test embeddings...');
    
    const populateResponse = await fetch(`${SUPABASE_URL}/functions/v1/populate-rag-embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content_types: ['profile'],
        batch_size: 5,
        dry_run: false
      })
    });

    if (populateResponse.ok) {
      const populateData = await populateResponse.json();
      console.log(`    ✅ Populated ${populateData.total_embeddings_created || 0} embeddings`);
    } else {
      console.log('    ⚠️  Could not populate embeddings, testing with existing data');
    }

    // Test RAG retrieval
    const testQuery = 'Find innovative entrepreneurs in AI technology';
    console.log(`    🔍 Testing RAG query: "${testQuery}"`);

    const ragResponse = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: testQuery,
        rag_enabled: true,
        max_context_items: 5,
        user_context: {
          is_authenticated: false,
          current_page: 'test'
        }
      })
    });

    const ragData = await ragResponse.json();
    
    if (ragData.success) {
      console.log(`    ✅ RAG query successful`);
      console.log(`    📊 Context items: ${ragData.rag_context_used?.length || 0}`);
      console.log(`    🎯 Query route: ${ragData.query_route}`);
      console.log(`    ⏱️  Processing time: ${ragData.processing_time_ms}ms`);
      return { success: true, contextItems: ragData.rag_context_used?.length || 0 };
    } else {
      console.log(`    ❌ RAG query failed: ${ragData.error}`);
      return { success: false, error: ragData.error };
    }
  } catch (error) {
    console.log(`    ❌ RAG test error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test AI chat integration
 */
async function testAIChat() {
  console.log('  💬 Testing AI chat integration...');
  
  const testMessages = [
    'Hello, I need help with my profile',
    'What innovators are available for collaboration?',
    'How can I improve my profile completion?'
  ];

  const results = [];

  for (const message of testMessages) {
    try {
      console.log(`    💭 Testing: "${message}"`);
      
      const response = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          rag_enabled: true,
          user_context: {
            is_authenticated: true,
            user_id: 'test-user-id',
            profile_type: 'innovator',
            profile_completion: 75,
            current_page: 'dashboard'
          }
        })
      });

      const data = await response.json();
      
      if (data.success) {
        console.log(`      ✅ Response received (${data.message?.length || 0} chars)`);
        console.log(`      🎯 Route: ${data.query_route}`);
        console.log(`      ⏱️  Time: ${data.processing_time_ms}ms`);
        results.push({ message, success: true, responseLength: data.message?.length || 0 });
      } else {
        console.log(`      ❌ Failed: ${data.error}`);
        results.push({ message, success: false, error: data.error });
      }
    } catch (error) {
      console.log(`      ❌ Error: ${error.message}`);
      results.push({ message, success: false, error: error.message });
    }
  }

  const successCount = results.filter(r => r.success).length;
  console.log(`  📊 AI Chat Results: ${successCount}/${testMessages.length} messages successful`);
  
  return { success: successCount > 0, results };
}

/**
 * Test conversation memory
 */
async function testConversationMemory() {
  console.log('  🧠 Testing conversation memory...');
  
  try {
    // Test conversation storage and retrieval
    const conversationId = 'test-conversation-' + Date.now();
    
    // Send first message
    const firstResponse = await fetch(`${SUPABASE_URL}/functions/v1/ai-enhanced-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'My name is John and I am an innovator',
        conversation_id: conversationId,
        user_context: {
          is_authenticated: true,
          user_id: 'test-user-memory',
          profile_type: 'innovator'
        }
      })
    });

    if (!firstResponse.ok) {
      throw new Error('First message failed');
    }

    // Send follow-up message
    const followUpResponse = await fetch(`${SUPABASE_URL}/functions/v1/ai-enhanced-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'What did I tell you about my name?',
        conversation_id: conversationId,
        user_context: {
          is_authenticated: true,
          user_id: 'test-user-memory',
          profile_type: 'innovator'
        }
      })
    });

    const followUpData = await followUpResponse.json();
    
    if (followUpData.success) {
      console.log('    ✅ Conversation memory test successful');
      console.log(`    💭 Response: ${followUpData.response?.substring(0, 100)}...`);
      return { success: true };
    } else {
      console.log(`    ❌ Conversation memory test failed: ${followUpData.error}`);
      return { success: false, error: followUpData.error };
    }
  } catch (error) {
    console.log(`    ❌ Conversation memory error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Test authentication awareness
 */
async function testAuthenticationAwareness() {
  console.log('  🔐 Testing authentication awareness...');
  
  try {
    // Test unauthenticated response
    const unauthResponse = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Help me find investors',
        user_context: {
          is_authenticated: false,
          current_page: 'home'
        }
      })
    });

    const unauthData = await unauthResponse.json();
    
    // Test authenticated response
    const authResponse = await fetch(`${SUPABASE_URL}/functions/v1/ai-chat`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: 'Help me find investors',
        user_context: {
          is_authenticated: true,
          user_id: 'test-auth-user',
          profile_type: 'innovator',
          profile_completion: 80,
          current_page: 'dashboard'
        }
      })
    });

    const authData = await authResponse.json();
    
    if (unauthData.success && authData.success) {
      console.log('    ✅ Authentication awareness test successful');
      console.log(`    👤 Unauthenticated response: ${unauthData.message?.substring(0, 80)}...`);
      console.log(`    🔑 Authenticated response: ${authData.message?.substring(0, 80)}...`);
      return { success: true };
    } else {
      console.log('    ❌ Authentication awareness test failed');
      return { success: false };
    }
  } catch (error) {
    console.log(`    ❌ Authentication awareness error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting comprehensive AI integration tests...\n');
  
  const results = [];
  
  for (const testCase of TEST_CASES) {
    console.log(`🧪 ${testCase.name}`);
    console.log(`   ${testCase.description}`);
    
    const startTime = Date.now();
    const result = await testCase.test();
    const duration = Date.now() - startTime;
    
    results.push({
      name: testCase.name,
      success: result.success,
      duration,
      ...result
    });
    
    console.log(`   ⏱️  Completed in ${duration}ms\n`);
  }
  
  // Summary
  console.log('📊 Test Summary');
  console.log('===============');
  
  const successCount = results.filter(r => r.success).length;
  const totalTests = results.length;
  
  console.log(`✅ Passed: ${successCount}/${totalTests} tests`);
  console.log(`❌ Failed: ${totalTests - successCount}/${totalTests} tests`);
  
  if (successCount === totalTests) {
    console.log('\n🎉 All tests passed! AI integration is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the output above for details.');
  }
  
  return results;
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(console.error);
}

module.exports = { runAllTests, TEST_CASES };
