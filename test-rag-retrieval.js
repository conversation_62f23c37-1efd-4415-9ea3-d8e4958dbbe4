#!/usr/bin/env node

/**
 * Test RAG retrieval with the newly inserted embedding
 */

const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

function generateFastEmbedding(text, dimensions = 384) {
  const embedding = new Array(dimensions).fill(0);
  
  const cleanText = text.toLowerCase().trim();
  const words = cleanText.split(/\s+/).filter(w => w.length > 0);
  const chars = cleanText.split('');
  
  for (let i = 0; i < dimensions; i++) {
    let value = 0;
    
    if (i < chars.length) {
      const char = chars[i];
      value += char.charCodeAt(0) / 127.0;
    }
    
    const wordIndex = i % words.length;
    if (words[wordIndex]) {
      const word = words[wordIndex];
      value += word.length / 15.0;
      value += word.charCodeAt(0) / 127.0;
    }
    
    value += Math.sin(i * 0.1) * 0.1;
    value += Math.cos(i * 0.05) * 0.1;
    
    const textHash = simpleHash(cleanText + i.toString());
    value += (textHash % 100) / 100.0;
    
    const semanticWords = ['innovation', 'technology', 'startup', 'investor', 'mentor', 'ai', 'business'];
    semanticWords.forEach((semWord, idx) => {
      if (cleanText.includes(semWord)) {
        value += Math.sin((i + idx) * 0.2) * 0.2;
      }
    });
    
    embedding[i] = Math.tanh(value);
  }
  
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
  if (magnitude > 0) {
    for (let i = 0; i < embedding.length; i++) {
      embedding[i] /= magnitude;
    }
  }
  
  return embedding;
}

function simpleHash(str) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return Math.abs(hash);
}

async function testRAGRetrieval() {
  console.log('🔍 Testing RAG Retrieval');
  console.log('========================');
  
  const testQueries = [
    'mentor expertise',
    'effy mento',
    'bio information',
    'AI technology innovation'
  ];
  
  for (const query of testQueries) {
    console.log(`\n🔎 Testing query: "${query}"`);
    
    try {
      // Generate embedding for the query
      const startTime = Date.now();
      const queryEmbedding = generateFastEmbedding(query);
      const embeddingTime = Date.now() - startTime;
      
      console.log(`  🧠 Generated query embedding in ${embeddingTime}ms`);
      
      // Test RAG retrieval
      const ragStartTime = Date.now();
      const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/get_rag_context`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
          'apikey': SUPABASE_ANON_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query_embedding: `[${queryEmbedding.join(',')}]`,
          max_context_items: 5,
          similarity_threshold: 0.1 // Low threshold to ensure we get results
        })
      });

      const ragTime = Date.now() - ragStartTime;
      
      if (response.ok) {
        const ragData = await response.json();
        console.log(`  ✅ RAG retrieval successful in ${ragTime}ms`);
        console.log(`  📊 Found ${ragData.length} context items`);
        
        ragData.forEach((item, index) => {
          console.log(`    ${index + 1}. Score: ${item.relevance_score?.toFixed(3)}`);
          console.log(`       Content: "${item.content_snippet?.substring(0, 60)}..."`);
          console.log(`       Metadata: ${JSON.stringify(item.metadata)}`);
        });
        
        if (ragData.length === 0) {
          console.log('  ⚠️  No results found - try lowering similarity threshold');
        }
      } else {
        const errorText = await response.text();
        console.log(`  ❌ RAG retrieval failed: ${response.status}`);
        console.log(`  📄 Error: ${errorText}`);
      }
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
    }
  }
}

async function testAnalyzeRAGSystem() {
  console.log('\n📈 Testing analyze_rag_system...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/analyze_rag_system`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });

    if (response.ok) {
      const data = await response.json();
      console.log('  ✅ RAG system analysis:');
      console.log('  📊 Results:', JSON.stringify(data, null, 2));
    } else {
      const errorText = await response.text();
      console.log(`  ❌ Analysis failed: ${response.status}`);
      console.log(`  📄 Error: ${errorText}`);
    }
  } catch (error) {
    console.log(`  ❌ Analysis error: ${error.message}`);
  }
}

async function runAllTests() {
  await testRAGRetrieval();
  await testAnalyzeRAGSystem();
  
  console.log('\n🎉 RAG testing complete!');
}

runAllTests().catch(console.error);
