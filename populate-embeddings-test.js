#!/usr/bin/env node

/**
 * Test and populate embeddings for RAG functionality
 */

const SUPABASE_URL = 'https://dpicnvisvxpmgjtbeicf.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRwaWNudmlzdnhwbWdqdGJlaWNmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM3OTE0MDgsImV4cCI6MjA1OTM2NzQwOH0.6x_KSp-mDrLNqZiSW3ZB4VFDPM5tQas0mc68Md-Uvrc';

console.log('🧠 Populating RAG Embeddings');
console.log('============================');

/**
 * Test populate-rag-embeddings function
 */
async function populateEmbeddings() {
  console.log('📊 Starting embedding population...');
  
  try {
    const startTime = Date.now();
    
    const response = await fetch(`${SUPABASE_URL}/functions/v1/populate-rag-embeddings`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        content_types: ['profile'], // Start with profiles only
        batch_size: 5, // Small batch to test
        force_regenerate: false,
        dry_run: false
      })
    });

    const data = await response.json();
    const totalTime = Date.now() - startTime;
    
    if (response.ok && data.success) {
      console.log('✅ Embedding population successful!');
      console.log(`📊 Total embeddings created: ${data.total_embeddings_created}`);
      console.log(`⏱️  Total time: ${totalTime}ms`);
      console.log(`📈 Processing details:`);
      
      data.content_processed?.forEach(item => {
        console.log(`  - ${item.content_type}: ${item.embeddings_created}/${item.records_found} embeddings created`);
        if (item.errors?.length > 0) {
          console.log(`    ❌ Errors: ${item.errors.join(', ')}`);
        }
      });
      
      if (data.errors?.length > 0) {
        console.log(`⚠️  General errors: ${data.errors.join(', ')}`);
      }
      
      return data;
    } else {
      console.log('❌ Embedding population failed');
      console.log(`📄 Response:`, JSON.stringify(data, null, 2));
      return null;
    }
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    return null;
  }
}

/**
 * Check current embedding status
 */
async function checkEmbeddingStatus() {
  console.log('📈 Checking current embedding status...');
  
  try {
    const response = await fetch(`${SUPABASE_URL}/rest/v1/rpc/analyze_rag_system`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    });

    if (response.ok) {
      const data = await response.json();
      console.log('📊 Current RAG system status:');
      data.forEach(metric => {
        console.log(`  ${metric.metric_name}: ${metric.metric_value} (${metric.status})`);
      });
      return data;
    } else {
      console.log('❌ Failed to check embedding status');
      return null;
    }
  } catch (error) {
    console.log(`❌ Error checking status: ${error.message}`);
    return null;
  }
}

/**
 * Test RAG retrieval after population
 */
async function testRAGRetrieval() {
  console.log('🔍 Testing RAG retrieval...');
  
  try {
    // First generate a test embedding
    const embedResponse = await fetch(`${SUPABASE_URL}/functions/v1/embed`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: 'AI technology innovation startup',
        model: 'gte-small' // Use faster model for testing
      })
    });

    if (!embedResponse.ok) {
      console.log('❌ Failed to generate test embedding');
      return;
    }

    const embedData = await embedResponse.json();
    if (!embedData.success) {
      console.log('❌ Embedding generation failed');
      return;
    }

    console.log(`✅ Generated test embedding: ${embedData.dimensions} dimensions in ${embedData.processing_time_ms}ms`);

    // Now test RAG retrieval
    const ragResponse = await fetch(`${SUPABASE_URL}/rest/v1/rpc/get_rag_context`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
        'apikey': SUPABASE_ANON_KEY,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query_embedding: `[${embedData.embedding.join(',')}]`,
        max_context_items: 5,
        similarity_threshold: 0.3
      })
    });

    if (ragResponse.ok) {
      const ragData = await ragResponse.json();
      console.log(`✅ RAG retrieval successful: ${ragData.length} context items found`);
      
      ragData.forEach((item, index) => {
        console.log(`  ${index + 1}. ${item.content_type} (score: ${item.relevance_score?.toFixed(3)})`);
        console.log(`     "${item.content_snippet?.substring(0, 100)}..."`);
      });
      
      return ragData;
    } else {
      console.log('❌ RAG retrieval failed');
      return null;
    }
  } catch (error) {
    console.log(`❌ RAG test error: ${error.message}`);
    return null;
  }
}

/**
 * Run complete embedding setup and test
 */
async function runCompleteTest() {
  console.log('🚀 Starting complete embedding setup and test...\n');
  
  // Step 1: Check current status
  await checkEmbeddingStatus();
  console.log('');
  
  // Step 2: Populate embeddings
  const populateResult = await populateEmbeddings();
  console.log('');
  
  if (populateResult && populateResult.total_embeddings_created > 0) {
    // Step 3: Check status after population
    await checkEmbeddingStatus();
    console.log('');
    
    // Step 4: Test RAG retrieval
    await testRAGRetrieval();
  } else {
    console.log('⚠️  Skipping RAG test due to embedding population failure');
  }
  
  console.log('\n✅ Complete test finished!');
}

// Run the complete test
runCompleteTest().catch(console.error);
