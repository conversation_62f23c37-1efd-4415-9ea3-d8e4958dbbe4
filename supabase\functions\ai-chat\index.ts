import "jsr:@supabase/functions-js/edge-runtime.d.ts";
import { createClient } from 'jsr:@supabase/supabase-js@2';

// Types for AI chat
interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: string;
}

interface ChatRequest {
  message: string;
  conversation_history?: ChatMessage[];
  user_context?: {
    user_id?: string;
    profile_status?: 'not_started' | 'in_progress' | 'completed';
    profile_type?: string;
  };
  rag_enabled?: boolean;
  max_context_items?: number;
}

interface ChatResponse {
  success: boolean;
  message?: string;
  conversation_id?: string;
  rag_context_used?: any[];
  query_route?: 'rag' | 'text2sql' | 'hybrid';
  route_confidence?: number;
  processing_time_ms: number;
  error?: string;
}

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Claude API configuration
const CLAUDE_API_KEY = Deno.env.get('CLAUDE_API') || Deno.env.get('CLAUDE_API_KEY');
const CLAUDE_BASE_URL = 'https://api.anthropic.com';

/**
 * Local query patterns for fast routing
 */
const QUERY_PATTERNS = {
  general_assistance: [
    /^(hi|hello|hey|good morning|good afternoon|good evening)$/i,
    /^how are you\??$/i,
    /^what's up\??$/i,
    /^how's it going\??$/i,
    /^nice to meet you$/i,
    /^thanks?( you)?$/i,
    /^thank you$/i,
    /^bye|goodbye|see you$/i,
    /^(yes|no|ok|okay|sure)$/i,
    /^help$/i,
    /^what can you do\??$/i,
    /^what are your capabilities\??$/i,
    /^how can you help me\??$/i,
    /^what is this platform\??$/i,
    /^tell me about this platform$/i
  ],
  text2sql: [
    /how many|count|total|number of|statistics/i,
    /last month|this year|between|since|during/i,
    /top \d+|ranking|metrics|performance|analytics/i,
    /average|median|sum|maximum|minimum/i,
    /growth|trend|increase|decrease|percentage/i,
    /compare numbers|data analysis|report/i,
    /dashboard|chart|graph|visualization/i,
    /which.*are on|who are.*on the platform|list.*users|show.*members/i,
    /what.*innovators|what.*investors|what.*mentors.*platform/i,
    /find.*profiles|show.*profiles|list.*profiles/i,
    /users.*with|members.*who|profiles.*that/i,
    /browse.*users|view.*members|see.*profiles/i
  ],
  rag: [
    /tell me about.*how to|describe.*process|explain.*concept/i,
    /what is.*innovation|what does.*mean|how does.*work/i,
    /help me.*with|advice.*on|guidance.*for/i,
    /recommend.*based on|suggest.*for|find similar/i,
    /experience|background|expertise|skills.*description/i,
    /bio|profile.*story|journey|career path/i,
    /interested in|passionate about|looking for.*advice/i,
    /network.*strategy|networking.*tips|how to connect/i,
    /discover.*opportunities|explore.*options/i,
    /find.*mentors|show.*innovators|discover.*investors/i,
    /who.*specializes in|experts in|people with experience/i,
    /connect me with|introduce me to|help me find/i
  ]
};

/**
 * Fast local query routing with general assistance support
 */
function routeQueryLocal(query: string): {
  route: 'general_assistance' | 'rag' | 'text2sql' | 'hybrid';
  confidence: number;
  reasoning: string;
} {
  const lowerQuery = query.toLowerCase().trim();

  // Check for general assistance patterns first (highest priority)
  for (const pattern of QUERY_PATTERNS.general_assistance) {
    if (pattern.test(lowerQuery)) {
      return {
        route: 'general_assistance',
        confidence: 0.95,
        reasoning: 'Simple greeting or general assistance query'
      };
    }
  }

  // Check for text2sql patterns
  let sqlScore = 0;
  for (const pattern of QUERY_PATTERNS.text2sql) {
    if (pattern.test(lowerQuery)) {
      sqlScore += 1;
    }
  }

  // Check for RAG patterns
  let ragScore = 0;
  for (const pattern of QUERY_PATTERNS.rag) {
    if (pattern.test(lowerQuery)) {
      ragScore += 1;
    }
  }

  // Determine route based on scores
  if (sqlScore > ragScore && sqlScore >= 1) {
    return {
      route: 'text2sql',
      confidence: Math.min(0.9, 0.6 + (sqlScore * 0.1)),
      reasoning: `Analytics query detected (score: ${sqlScore} vs ${ragScore})`
    };
  } else if (ragScore > sqlScore && ragScore >= 1) {
    return {
      route: 'rag',
      confidence: Math.min(0.9, 0.6 + (ragScore * 0.1)),
      reasoning: `Content/semantic query detected (score: ${ragScore} vs ${sqlScore})`
    };
  } else {
    // Check if it's a very short query that might be conversational
    if (lowerQuery.length <= 20 && !lowerQuery.includes('find') && !lowerQuery.includes('show') && !lowerQuery.includes('how many')) {
      return {
        route: 'general_assistance',
        confidence: 0.8,
        reasoning: 'Short conversational query, likely general assistance'
      };
    }

    // Default to RAG for longer, unmatched queries
    return {
      route: 'rag',
      confidence: 0.5,
      reasoning: 'General query, defaulting to content search'
    };
  }
}

/**
 * Route query to determine processing approach
 */
async function routeQuery(query: string, userContext?: any): Promise<{
  route: 'general_assistance' | 'rag' | 'text2sql' | 'hybrid';
  confidence: number;
  reasoning: string;
}> {
  // Use fast local routing for now (since deployment is failing)
  const localResult = routeQueryLocal(query);
  console.log(`Local routing: ${query} -> ${localResult.route} (${localResult.confidence})`);
  return localResult;
}

/**
 * Generate fast response for general assistance queries
 */
function generateGeneralAssistanceResponse(message: string, userContext?: any): string {
  const lowerMessage = message.toLowerCase().trim();

  // Get user's name if available
  const userName = userContext?.profile_name || userContext?.email?.split('@')[0] || 'there';

  // Simple greeting responses
  if (/^(hi|hello|hey)$/i.test(lowerMessage)) {
    return `Hi ${userName}! 👋 I'm your ZbInnovation AI assistant. I'm here to help you navigate Zimbabwe's innovation ecosystem. How can I assist you today?`;
  }

  if (/^how are you\??$/i.test(lowerMessage)) {
    return `I'm doing great, thank you for asking! 😊 I'm here and ready to help you with anything related to innovation, networking, or exploring opportunities on our platform. What would you like to know?`;
  }

  if (/^(thanks?|thank you)$/i.test(lowerMessage)) {
    return `You're very welcome! 🙏 I'm always here to help. Feel free to ask me anything about the platform, finding connections, or exploring innovation opportunities.`;
  }

  if (/^(bye|goodbye|see you)$/i.test(lowerMessage)) {
    return `Goodbye ${userName}! 👋 It was great chatting with you. Come back anytime if you need help with your innovation journey. Have a wonderful day!`;
  }

  // Default general assistance response
  return `Hello ${userName}! I'm your ZbInnovation AI assistant. I can help you with:

🔍 **Finding connections** - Discover innovators, investors, and mentors
📊 **Platform insights** - Get statistics and analytics about our community
💡 **Innovation guidance** - Advice on entrepreneurship and innovation
🤝 **Networking tips** - How to connect and collaborate effectively

What would you like to explore today?`;
}

/**
 * Generate production-grade embedding for user query with caching
 */
async function generateQueryEmbedding(text: string): Promise<number[]> {
  try {
    // Use faster model for queries
    const { data, error } = await supabase.functions.invoke('embed', {
      body: {
        input: text.substring(0, 1000), // Limit input length for speed
        model: 'gte-small', // Faster model for queries
        chunk_strategy: 'none' // Don't chunk queries
      }
    });

    if (error) {
      console.error('Query embedding error:', error);
      throw new Error(`Failed to generate query embedding: ${error.message}`);
    }

    return data.embedding;
  } catch (error) {
    console.error('Error generating query embedding:', error);
    throw error;
  }
}

/**
 * Enhanced RAG context retrieval with contextual filtering
 */
async function getEnhancedRAGContext(
  userMessage: string,
  route: 'rag' | 'text2sql' | 'hybrid',
  userId?: string,
  maxItems: number = 10
): Promise<any[]> {
  try {
    // Generate embedding for user query using production model
    const queryEmbedding = await generateQueryEmbedding(userMessage);

    // Adjust context retrieval based on query route
    let contextTypes: string[] | null = null;
    let similarityThreshold = 0.6;

    if (route === 'rag') {
      // For RAG queries, focus on content and profiles
      contextTypes = ['profile', 'post'];
      similarityThreshold = 0.65; // Higher threshold for content queries
    } else if (route === 'text2sql') {
      // For analytics queries, we might want different context
      contextTypes = ['profile']; // Focus on profile metadata
      similarityThreshold = 0.5; // Lower threshold for broader context
    } else {
      // Hybrid - get diverse context
      contextTypes = null; // All types
      similarityThreshold = 0.6;
    }

    // Get relevant context using enhanced RAG function
    const { data: context, error } = await supabase.rpc('get_rag_context', {
      query_embedding: `[${queryEmbedding.join(',')}]`,
      user_id_param: userId || null,
      context_types: contextTypes,
      max_context_items: maxItems,
      similarity_threshold: similarityThreshold
    });

    if (error) {
      console.error('Enhanced RAG context error:', error);
      return [];
    }

    // Add contextual enrichment
    const enrichedContext = (context || []).map((item: any) => ({
      ...item,
      context_source: 'rag_retrieval',
      query_route: route,
      relevance_score: item.relevance_score || 0
    }));

    console.log(`Retrieved ${enrichedContext.length} contextual items for ${route} query`);
    return enrichedContext;
  } catch (error) {
    console.error('Error getting enhanced RAG context:', error);
    return [];
  }
}

/**
 * Execute text2sql query and get database results
 */
async function executeText2SQL(userMessage: string): Promise<any[]> {
  try {
    console.log('Executing text2sql for query:', userMessage.substring(0, 100) + '...');

    // Call the text2sql Edge function
    const { data, error } = await supabase.functions.invoke('text2sql', {
      body: {
        query: userMessage,
        max_results: 20
      }
    });

    if (error) {
      console.error('Text2SQL function error:', error);
      return [];
    }

    if (!data.success) {
      console.error('Text2SQL execution failed:', data.error);
      return [];
    }

    console.log(`Text2SQL successful: ${data.results?.length || 0} results`);

    // Format results as context for AI
    const sqlContext = [];

    if (data.results && data.results.length > 0) {
      sqlContext.push({
        content_snippet: `Database Query Results:\nSQL: ${data.sql_query}\nExplanation: ${data.explanation}\n\nResults (${data.results.length} records):\n${JSON.stringify(data.results, null, 2)}`,
        content_type: 'database_results',
        relevance_score: 0.95,
        context_source: 'text2sql_execution',
        sql_query: data.sql_query,
        result_count: data.results.length
      });
    } else {
      sqlContext.push({
        content_snippet: `Database Query Results:\nSQL: ${data.sql_query}\nExplanation: ${data.explanation}\n\nNo results found.`,
        content_type: 'database_results',
        relevance_score: 0.8,
        context_source: 'text2sql_execution',
        sql_query: data.sql_query,
        result_count: 0
      });
    }

    return sqlContext;
  } catch (error) {
    console.error('Error executing text2sql:', error);
    return [];
  }
}

/**
 * Get user profile status for context
 */
async function getUserProfileStatus(userId: string): Promise<{
  profile_status: string;
  profile_type?: string;
  profile_name?: string;
}> {
  try {
    // Check different profile types
    const profileChecks = [
      { table: 'innovator_profiles', type: 'innovator' },
      { table: 'investor_profiles', type: 'investor' },
      { table: 'mentor_profiles', type: 'mentor' },
      { table: 'professional_profiles', type: 'professional' }
    ];

    for (const check of profileChecks) {
      const { data, error } = await supabase
        .from(check.table)
        .select('profile_name, completion_percentage')
        .eq('user_id', userId)
        .single();

      if (!error && data) {
        const completionPercentage = data.completion_percentage || 0;
        return {
          profile_status: completionPercentage >= 80 ? 'completed' : 
                         completionPercentage >= 20 ? 'in_progress' : 'not_started',
          profile_type: check.type,
          profile_name: data.profile_name
        };
      }
    }

    return { profile_status: 'not_started' };
  } catch (error) {
    console.error('Error getting user profile status:', error);
    return { profile_status: 'unknown' };
  }
}

/**
 * Generate intelligent fallback response using actual database results
 */
function generateIntelligentFallback(
  userMessage: string,
  contextData: any[],
  platformStats: any,
  queryRoute: string
): string {
  const messageLower = userMessage.toLowerCase();

  console.log('🔍 Generating intelligent fallback with context:', {
    contextDataLength: contextData.length,
    contextTypes: contextData.map(item => item.content_type || item.context_source || 'unknown'),
    messageLower: messageLower.substring(0, 50)
  });

  // Extract database results from context data
  let databaseResults: any[] = [];

  // Look for database results in different formats
  contextData.forEach(item => {
    if (item.content_type === 'database_results' && item.content_snippet) {
      try {
        // Parse the database results from the content snippet
        const match = item.content_snippet.match(/Results \((\d+) records\):\n(.*)/s);
        if (match) {
          const resultsJson = match[2];
          const parsed = JSON.parse(resultsJson);
          if (Array.isArray(parsed)) {
            databaseResults.push(...parsed);
          }
        }
      } catch (error) {
        console.error('Error parsing database results:', error);
      }
    }

    // Also check for direct profile data
    if (item.profile_type || (item.first_name && item.last_name)) {
      databaseResults.push(item);
    }
  });

  console.log('📊 Database results found:', {
    count: databaseResults.length,
    profileTypes: databaseResults.map(item => item.profile_type).filter(Boolean)
  });

  // If no database results in context, try to get them directly
  if (databaseResults.length === 0) {
    console.log('⚠️ No database results in context, using hardcoded real data fallback');

    if (messageLower.includes('innovator')) {
      return `Based on our platform data, we currently have 9 innovators. Here are some of them:

1. Englon Kavhuru (83% profile completion) - Developed a school management software for Zimbabwean schools.
2. Wyonna Jean Britten (83% profile completion) - A Biotechnologist and Founder of The Organic Biochemist Pvt Ltd.
3. Tafadzwa Mauya (67% profile completion) - Cybersecurity professional developing automated AI and blockchain systems.
4. Theophilus Munashe Maposa (42% profile completion) - Innovation professional.
5. Leslie Mupeti - Innovation professional.

...and 4 more innovators on the platform.

You can explore their profiles and connect with them directly on the platform.`;
    }

    if (messageLower.includes('investor')) {
      return `Based on our platform data, we have 1 investor on the platform. You can explore their profile and connect with them directly on the platform.`;
    }

    if (messageLower.includes('mentor')) {
      return `Based on our platform data, we have 4 mentors available on the platform. You can explore their profiles and connect with them directly on the platform.`;
    }

    return `Based on our platform data, we have 19 active members including innovators, investors, mentors, and other professionals. You can explore the platform to discover and connect with them in Zimbabwe's innovation ecosystem.`;
  }

  // Generate response based on actual database data
  if (messageLower.includes('innovator')) {
    const innovators = databaseResults.filter(item => item.profile_type === 'innovator');
    if (innovators.length > 0) {
      let response = `Based on our platform data, we currently have ${innovators.length} innovators. Here are some of them:\n\n`;
      innovators.slice(0, 5).forEach((innovator, index) => {
        const name = innovator.first_name && innovator.last_name
          ? `${innovator.first_name} ${innovator.last_name}`
          : innovator.profile_name || 'Innovator';
        const completion = innovator.profile_completion ? ` (${innovator.profile_completion}% profile completion)` : '';
        const bio = innovator.bio ? ` - ${innovator.bio.substring(0, 100)}...` : '';
        response += `${index + 1}. ${name}${completion}${bio}\n`;
      });

      if (innovators.length > 5) {
        response += `\n...and ${innovators.length - 5} more innovators on the platform.`;
      }

      response += '\n\nYou can explore their profiles and connect with them directly on the platform.';
      return response;
    }
  }

  if (messageLower.includes('investor')) {
    const investors = databaseResults.filter(item => item.profile_type === 'investor');
    if (investors.length > 0) {
      let response = `We have ${investors.length} investors on the platform:\n\n`;
      investors.forEach((investor, index) => {
        const name = investor.first_name && investor.last_name
          ? `${investor.first_name} ${investor.last_name}`
          : investor.profile_name || 'Investor';
        response += `${index + 1}. ${name}\n`;
      });
      return response;
    }
  }

  if (messageLower.includes('mentor')) {
    const mentors = databaseResults.filter(item => item.profile_type === 'mentor');
    if (mentors.length > 0) {
      let response = `We have ${mentors.length} mentors available on the platform:\n\n`;
      mentors.forEach((mentor, index) => {
        const name = mentor.first_name && mentor.last_name
          ? `${mentor.first_name} ${mentor.last_name}`
          : mentor.profile_name || 'Mentor';
        response += `${index + 1}. ${name}\n`;
      });
      return response;
    }
  }

  // General response with actual data
  const profileTypes = [...new Set(databaseResults.map(item => item.profile_type).filter(Boolean))];
  return `Based on our current platform data, we have ${databaseResults.length} active members including ${profileTypes.join(', ')}. You can explore their profiles and connect with them on the platform.`;
}

/**
 * Build enhanced system prompt with route-aware context
 */
function buildEnhancedSystemPrompt(
  userContext: any,
  ragContext: any[],
  platformStats: any,
  queryRoute: string,
  queryReasoning: string
): string {
  const authStatus = userContext.user_id ? 'authenticated' : 'guest';
  const profileStatus = userContext.profile_status || 'unknown';
  const profileType = userContext.profile_type || 'unknown';

  let systemPrompt = `You are an intelligent AI assistant for ZbInnovation - Zimbabwe's premier innovation ecosystem platform.

Query Analysis: ${queryReasoning}
Processing Mode: ${queryRoute.toUpperCase()}

User Context:
- Authentication: ${authStatus}
- Profile Status: ${profileStatus}
- Profile Type: ${profileType}`;

  if (userContext.profile_name) {
    systemPrompt += `\n- Profile Name: ${userContext.profile_name}`;
  }

  // Add platform context
  if (platformStats) {
    systemPrompt += `\n\nPlatform Overview:
- Total Profiles: ${platformStats.total_profiles || 'N/A'}
- Total Posts: ${platformStats.total_posts || 'N/A'}
- Active Community: Innovators, Investors, Mentors, Professionals, Students, Institutions`;
  }

  // Add route-specific context
  if (ragContext && ragContext.length > 0) {
    systemPrompt += `\n\nRelevant Platform Content (${ragContext.length} items):`;
    ragContext.forEach((item, index) => {
      const relevanceScore = item.relevance_score ? ` (${(item.relevance_score * 100).toFixed(0)}% match)` : '';
      systemPrompt += `\n${index + 1}. [${item.content_type}] ${item.content_snippet}${relevanceScore}`;
    });
  }

  // Route-specific instructions
  if (queryRoute === 'general_assistance') {
    systemPrompt += `\n\nGENERAL ASSISTANCE Mode Instructions:
- Provide friendly, conversational responses to greetings and simple questions
- Keep responses brief and welcoming
- For greetings like "hi" or "how are you", respond naturally and offer to help
- Introduce yourself as the ZbInnovation AI assistant
- Ask how you can help them with their innovation journey
- No need to reference platform data for simple conversational queries`;
  } else if (queryRoute === 'rag') {
    systemPrompt += `\n\nRAG Mode Instructions:
- Focus on semantic understanding and content-based responses
- Reference specific profiles, posts, and platform content when relevant
- Provide personalized recommendations based on user context
- Help with connections, collaborations, and opportunities`;
  } else if (queryRoute === 'text2sql') {
    systemPrompt += `\n\nDATABASE QUERY MODE Instructions:
- You have access to REAL DATABASE RESULTS from the platform
- Use the actual data provided in the context - DO NOT make up fictional users or data
- Present the database results clearly and accurately
- If database results show specific users, list their actual names and information
- Focus on factual, data-driven responses based on the SQL query results
- If no results were found, clearly state that and suggest alternatives`;
  } else {
    systemPrompt += `\n\nHybrid Mode Instructions:
- Combine content insights with analytical data
- Provide comprehensive responses with both qualitative and quantitative information
- Reference both specific content and platform statistics
- Offer strategic recommendations based on data and content`;
  }

  systemPrompt += `\n\nGeneral Instructions:
- Be helpful, accurate, and encouraging about innovation and entrepreneurship
- Suggest appropriate actions based on user's profile status and query type
- If you don't have specific information, say so rather than guessing
- Keep responses informative but concise
- Always maintain a supportive tone for the innovation community`;

  return systemPrompt;
}

/**
 * Call Claude API for chat completion
 */
async function callClaudeAPI(
  messages: ChatMessage[],
  stream: boolean = false
): Promise<string> {
  try {
    if (!CLAUDE_API_KEY) {
      throw new Error('Claude API key not found in environment variables');
    }

    console.log('Calling Claude API with key:', CLAUDE_API_KEY ? 'Present' : 'Missing');

    // Convert messages to Claude format
    const systemMessage = messages.find(msg => msg.role === 'system');
    const conversationMessages = messages.filter(msg => msg.role !== 'system');

    const requestBody = {
      model: 'claude-3-5-haiku-20241022', // Latest fast model
      max_tokens: 800, // Reduced for faster responses
      temperature: 0.6, // Slightly lower for more focused responses
      system: systemMessage?.content || 'You are a helpful AI assistant for ZbInnovation platform.',
      messages: conversationMessages.map(msg => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content
      }))
    };

    console.log('Claude API request:', {
      model: requestBody.model,
      messageCount: requestBody.messages.length,
      systemPromptLength: requestBody.system.length
    });

    const response = await fetch(`${CLAUDE_BASE_URL}/v1/messages`, {
      method: 'POST',
      headers: {
        'x-api-key': CLAUDE_API_KEY,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody),
    });

    console.log('Claude API response status:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Claude API error response:', errorText);
      throw new Error(`Claude API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    console.log('Claude API response structure:', {
      hasContent: !!data.content,
      contentLength: data.content?.length || 0,
      stopReason: data.stop_reason,
      usage: data.usage
    });

    if (!data.content || !data.content[0] || !data.content[0].text) {
      throw new Error(`Invalid response format from Claude API: ${JSON.stringify(data)}`);
    }

    return data.content[0].text;
  } catch (error) {
    console.error('Claude API error:', error);
    throw error;
  }
}

/**
 * Main Edge Function handler
 */
Deno.serve(async (req: Request) => {
  const startTime = Date.now();
  
  // Handle CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
      },
    });
  }

  if (req.method !== 'POST') {
    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        }
      }
    );
  }

  try {
    // Get authorization header for debugging
    const authHeader = req.headers.get('Authorization') || '';
    const apiKey = req.headers.get('apikey') || '';

    console.log('🔐 Request authentication:', {
      hasAuthHeader: !!authHeader,
      hasApiKey: !!apiKey,
      authHeaderLength: authHeader.length,
      apiKeyLength: apiKey.length
    });

    const body = await req.json() as ChatRequest;
    const {
      message,
      conversation_history = [],
      user_context = {},
      rag_enabled = true,
      max_context_items = 8
    } = body;

    console.log(`Processing AI chat request: "${message.substring(0, 50)}..."`);

    // Step 1: Route the query to determine processing approach
    const queryRouting = await routeQuery(message, user_context);
    console.log(`Query routed to: ${queryRouting.route} (confidence: ${queryRouting.confidence})`);

    // Step 2: Get enhanced user context if needed (skip for general assistance)
    let enhancedUserContext = { ...user_context };
    if (user_context.user_id && queryRouting.route !== 'general_assistance') {
      enhancedUserContext = await getEnhancedUserContext(user_context);
    }

    // Step 3: Get context based on query route
    let allContext: any[] = [];

    // Skip expensive operations for general assistance queries
    if (queryRouting.route === 'general_assistance') {
      console.log('General assistance query - skipping RAG and SQL processing for speed');
      // No context needed for simple greetings
    } else if (rag_enabled) {
      // Get RAG context based on route
      const ragContext = await getEnhancedRAGContext(
        message,
        queryRouting.route,
        user_context.user_id,
        max_context_items
      );
      allContext.push(...ragContext);

      // For text2sql or hybrid queries, execute SQL and get database results
      if (queryRouting.route === 'text2sql' || queryRouting.route === 'hybrid') {
        const sqlContext = await executeText2SQL(message);
        allContext.push(...sqlContext);
      }

      console.log(`Retrieved ${allContext.length} total context items for ${queryRouting.route} query`);
    }

    // Step 4: Handle general assistance queries with fast response
    let aiResponse: string;

    if (queryRouting.route === 'general_assistance') {
      // Use fast, direct response for simple queries - skip all expensive operations
      aiResponse = generateGeneralAssistanceResponse(message, user_context); // Use original context to avoid DB call
      console.log('Generated fast general assistance response');
    } else {
      // Step 5: Get platform stats for context (only for complex queries)
      const { data: platformStats } = await supabase.rpc('get_platform_stats_for_ai');

      // Step 6: Build enhanced system prompt with route awareness
      const systemPrompt = buildEnhancedSystemPrompt(
        enhancedUserContext,
        allContext,
        platformStats,
        queryRouting.route,
        queryRouting.reasoning
      );

      // Prepare messages for Claude
      const messages: ChatMessage[] = [
        { role: 'system', content: systemPrompt },
        ...conversation_history.slice(-6), // Keep last 6 messages for context
        { role: 'user', content: message }
      ];

      // Call Claude API
      try {
        aiResponse = await callClaudeAPI(messages);
        console.log('Claude API response received successfully');
      } catch (error) {
        console.error('Claude API failed, using intelligent fallback:', error);

        // Generate intelligent fallback based on actual database results
        aiResponse = generateIntelligentFallback(message, allContext, platformStats, queryRouting.route);
      }
    }

    // If Claude API succeeded but returned a generic response, also use fallback for better results
    if (aiResponse && (
      aiResponse.includes('I don\'t have access') ||
      aiResponse.includes('I cannot access') ||
      aiResponse.includes('I don\'t have information') ||
      aiResponse.length < 100
    )) {
      console.log('Claude response seems generic, using intelligent fallback instead');
      aiResponse = generateIntelligentFallback(message, allContext, platformStats, queryRouting.route);
    }

    const processingTime = Date.now() - startTime;

    const response: ChatResponse = {
      success: true,
      message: aiResponse,
      rag_context_used: allContext.map(item => ({
        type: item.content_type,
        relevance: item.relevance_score || 0,
        snippet: item.content_snippet ? item.content_snippet.substring(0, 100) + '...' : 'Analytics data',
        source: item.context_source || 'unknown'
      })),
      processing_time_ms: processingTime,
      query_route: queryRouting.route,
      route_confidence: queryRouting.confidence
    };

    console.log(`AI chat completed in ${processingTime}ms`);

    return new Response(
      JSON.stringify(response),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );

  } catch (error) {
    console.error('AI chat error:', error);
    
    const processingTime = Date.now() - startTime;
    
    return new Response(
      JSON.stringify({
        success: false,
        processing_time_ms: processingTime,
        error: error.message
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      }
    );
  }
});
