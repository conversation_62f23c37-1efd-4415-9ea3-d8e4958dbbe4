<template>
  <div v-if="triggers.length > 0" class="smart-ai-triggers">
    <!-- Compact Mode (for sidebars, headers) -->
    <div v-if="mode === 'compact'" class="compact-triggers">
      <q-btn-dropdown
        :color="primaryColor"
        :icon="primaryIcon"
        :label="compactLabel"
        dropdown-icon="expand_more"
        flat
        dense
        class="ai-trigger-dropdown"
      >
        <q-list>
          <q-item
            v-for="trigger in triggers"
            :key="trigger.id"
            clickable
            v-close-popup
            @click="handleTrigger(trigger)"
            class="trigger-item"
          >
            <q-item-section avatar>
              <q-icon :name="trigger.icon" :color="trigger.color" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ trigger.label }}</q-item-label>
              <q-item-label caption>{{ trigger.tooltip }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </q-btn-dropdown>
    </div>

    <!-- Card Mode (for main content areas) -->
    <q-card v-else-if="mode === 'card'" class="ai-triggers-card" flat bordered>
      <q-card-section class="q-pb-sm">
        <div class="row items-center">
          <div class="col">
            <div class="text-h6 text-primary">
              <q-icon name="smart_toy" class="q-mr-sm" />
              AI Assistant
            </div>
            <div class="text-caption text-grey-6">
              Get contextual help and suggestions
            </div>
          </div>
          <div class="col-auto">
            <q-btn
              v-if="showMinimize"
              flat
              dense
              round
              icon="minimize"
              @click="$emit('minimize')"
              class="text-grey-6"
            />
          </div>
        </div>
      </q-card-section>

      <q-card-section class="q-pt-none">
        <div class="row q-gutter-sm">
          <div
            v-for="trigger in triggers"
            :key="trigger.id"
            class="col-12 col-sm-6 col-md-4"
          >
            <q-btn
              :color="trigger.color"
              :icon="trigger.icon"
              :label="trigger.label"
              outline
              no-caps
              class="full-width trigger-btn"
              @click="handleTrigger(trigger)"
            >
              <q-tooltip>{{ trigger.tooltip }}</q-tooltip>
            </q-btn>
          </div>
        </div>
      </q-card-section>
    </q-card>

    <!-- Floating Mode (for floating action buttons) -->
    <div v-else-if="mode === 'floating'" class="floating-triggers">
      <q-fab
        :color="primaryColor"
        :icon="primaryIcon"
        direction="up"
        class="ai-fab"
      >
        <q-fab-action
          v-for="trigger in triggers.slice(0, 4)"
          :key="trigger.id"
          :color="trigger.color"
          :icon="trigger.icon"
          @click="handleTrigger(trigger)"
          class="trigger-fab-action"
        >
          <q-tooltip anchor="center left" self="center right">
            {{ trigger.tooltip }}
          </q-tooltip>
        </q-fab-action>
      </q-fab>
    </div>

    <!-- Inline Mode (for inline buttons) -->
    <div v-else class="inline-triggers">
      <div class="row q-gutter-xs">
        <div v-for="trigger in triggers" :key="trigger.id" class="col-auto">
          <q-btn
            :color="trigger.color"
            :icon="trigger.icon"
            :label="showLabels ? trigger.label : undefined"
            :size="buttonSize"
            :outline="outline"
            :flat="flat"
            @click="handleTrigger(trigger)"
            class="trigger-btn"
          >
            <q-tooltip v-if="!showLabels">{{ trigger.tooltip }}</q-tooltip>
          </q-btn>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { aiSmartTriggerService, type AITriggerConfig } from '@/services/aiSmartTriggerService'

// Props
interface Props {
  page: string
  section?: string
  mode?: 'compact' | 'card' | 'floating' | 'inline'
  context?: any
  showLabels?: boolean
  buttonSize?: string
  outline?: boolean
  flat?: boolean
  showMinimize?: boolean
  maxTriggers?: number
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'inline',
  showLabels: true,
  buttonSize: 'md',
  outline: false,
  flat: false,
  showMinimize: false,
  maxTriggers: 6
})

// Emits
const emit = defineEmits<{
  minimize: []
  triggerActivated: [triggerId: string]
}>()

// Reactive data
const triggers = ref<AITriggerConfig[]>([])
const loading = ref(false)

// Computed properties
const primaryColor = computed(() => {
  return triggers.value.length > 0 ? triggers.value[0].color : 'primary'
})

const primaryIcon = computed(() => {
  return triggers.value.length > 0 ? 'smart_toy' : 'help'
})

const compactLabel = computed(() => {
  return props.mode === 'compact' ? 'AI Help' : ''
})

// Methods
const loadTriggers = () => {
  try {
    const contextualTriggers = aiSmartTriggerService.getContextualTriggers(
      props.page,
      props.section,
      props.context
    )
    
    // Limit number of triggers if specified
    triggers.value = contextualTriggers.slice(0, props.maxTriggers)
  } catch (error) {
    console.error('Error loading AI triggers:', error)
    triggers.value = []
  }
}

const handleTrigger = async (trigger: AITriggerConfig) => {
  try {
    loading.value = true
    emit('triggerActivated', trigger.id)
    
    // Trigger the AI chat with context
    await aiSmartTriggerService.triggerAIChat(trigger.id, trigger.context)
    
  } catch (error) {
    console.error('Error handling AI trigger:', error)
  } finally {
    loading.value = false
  }
}

// Listen for context changes
const handleContextChange = () => {
  loadTriggers()
}

// Lifecycle
onMounted(() => {
  loadTriggers()
  
  // Listen for route changes or context updates
  window.addEventListener('ai-context-changed', handleContextChange)
})

onUnmounted(() => {
  window.removeEventListener('ai-context-changed', handleContextChange)
})

// Watch for prop changes
import { watch } from 'vue'
watch([() => props.page, () => props.section, () => props.context], () => {
  loadTriggers()
}, { deep: true })
</script>

<style scoped>
.smart-ai-triggers {
  position: relative;
}

.ai-triggers-card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.trigger-btn {
  transition: all 0.2s ease;
  text-transform: none;
}

.trigger-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.trigger-item {
  border-radius: 4px;
  margin: 2px 0;
}

.trigger-item:hover {
  background-color: rgba(25, 118, 210, 0.04);
}

.ai-trigger-dropdown {
  border-radius: 20px;
}

.compact-triggers {
  display: flex;
  align-items: center;
}

.floating-triggers {
  position: fixed;
  bottom: 80px;
  right: 20px;
  z-index: 1000;
}

.ai-fab {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.trigger-fab-action {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.inline-triggers {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .ai-triggers-card .row > div {
    min-width: 100%;
  }
  
  .floating-triggers {
    bottom: 70px;
    right: 15px;
  }
}

/* Animation for trigger appearance */
.trigger-btn {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state */
.trigger-btn.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Color variations for different trigger types */
.trigger-btn.help-trigger {
  border-color: #2196F3;
}

.trigger-btn.completion-trigger {
  border-color: #FF9800;
}

.trigger-btn.networking-trigger {
  border-color: #4CAF50;
}

.trigger-btn.discovery-trigger {
  border-color: #9C27B0;
}

.trigger-btn.optimization-trigger {
  border-color: #673AB7;
}
</style>
